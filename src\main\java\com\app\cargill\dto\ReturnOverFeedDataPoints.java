/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReturnOverFeedDataPoints {

  private Double returnOverFeed;
  private Double totalFeedCost;
  private Double totalRevenue;
  private String xAxis;
  
  
  public String getXAxis() {
	    return this.xAxis;
	  }
}
