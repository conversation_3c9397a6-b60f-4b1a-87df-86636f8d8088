/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MilkProductionSummaryDto implements Serializable {
  private static final long serialVersionUID = 1L;

  private Double averageMilkProductionAnimalsInTankKg;
  private Double averageMilkProductionLitresPerCowPerDay;
  private Double dairyEfficiency;
  private MilkComponentDto butterfat;
  private MilkComponentDto protein;
  private MilkComponentDto lactoseAndOtherSolids;
}
