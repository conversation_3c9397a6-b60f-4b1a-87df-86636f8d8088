/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.components.PlayWrightComponent;
import com.app.cargill.constants.LactationStage;
import com.app.cargill.constants.LangCodes;
import com.app.cargill.constants.MetabolicIncidenceGraphExportType;
import com.app.cargill.constants.MetabolicTypeKeys;
import com.app.cargill.constants.MilkSoldEvaluationExportChartVariations;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.dto.BCSAnimalAnalysisRecordDto;
import com.app.cargill.dto.BCSPenAnalysisCategoryDto;
import com.app.cargill.dto.BCSPenAnalysisReportDto;
import com.app.cargill.dto.BCSToolHeardAnalysisReportDto;
import com.app.cargill.dto.BcsAnimalAnalysisReportDto;
import com.app.cargill.dto.CudChewingHerdAnalysisReportDto;
import com.app.cargill.dto.CudChewingPenAnalysisChewingDto;
import com.app.cargill.dto.CudChewingPenAnalysisReportDto;
import com.app.cargill.dto.DialGraphReportDto;
import com.app.cargill.dto.DualYAxisGraphDto;
import com.app.cargill.dto.ForagePennStateOnScreenPercentageDto;
import com.app.cargill.dto.ForagePennStateReportDto;
import com.app.cargill.dto.GaugeColorDto;
import com.app.cargill.dto.HeatStressDataPointsDto;
import com.app.cargill.dto.HeatStressReportDto;
import com.app.cargill.dto.LocomotionAnimalAnalysisRecordDto;
import com.app.cargill.dto.LocomotionAnimalAnalysisReportDto;
import com.app.cargill.dto.LocomotionScoreHerdAnalysisReportDto;
import com.app.cargill.dto.LocomotionScorePenAnalysisCategoryDto;
import com.app.cargill.dto.LocomotionScorePenAnalysisReportDto;
import com.app.cargill.dto.MetabolicDisorderCostPerCowDto;
import com.app.cargill.dto.MetabolicIncidenceReportDto;
import com.app.cargill.dto.MilkSoldEvaluationExportChartDto;
import com.app.cargill.dto.MilkSoldEvaluationReportDto;
import com.app.cargill.dto.NotesDto;
import com.app.cargill.dto.PenTimeBudgetPotentialMilkLossGainReportDto;
import com.app.cargill.dto.PenTimeBudgetTimeAvailableForRestingReportDto;
import com.app.cargill.dto.ProfitabilityAnalysisReportDto;
import com.app.cargill.dto.ReturnOverFeedDataPoints;
import com.app.cargill.dto.ReturnOverFeedReportDto;
import com.app.cargill.dto.RoboticMilkEvaluationReportDto;
import com.app.cargill.dto.RumenFillHealthHerdAnalysisReportDto;
import com.app.cargill.dto.RumenFillHealthPenAnalysisCategoryDto;
import com.app.cargill.dto.RumenFillHealthPenAnalysisReportDto;
import com.app.cargill.dto.RumenHealthMSHerdAnalysisReportDto;
import com.app.cargill.dto.RumenHealthMSPenAnalysisCategoryDto;
import com.app.cargill.dto.RumenHealthMSPenAnalysisReportDto;
import com.app.cargill.dto.RumenHealthManureScreeningOnScreenPercentageDto;
import com.app.cargill.dto.RumenHealthManureScreeningPenAnalysisReportDto;
import com.app.cargill.dto.RumenHealthTMRParticleScoreHerdAnalysisDto;
import com.app.cargill.dto.RumenHealthTMRParticleScoreHerdAnalysisReportDto;
import com.app.cargill.dto.RumenHealthTMRParticleScoreOnScreenPercentageDto;
import com.app.cargill.dto.RumenHealthTMRParticleScorePenAnalysisReportDto;
import com.app.cargill.dto.SingleYAxisGraphDto;
import com.app.cargill.dto.VisitReportAnimalDetailsDto;
import com.app.cargill.dto.VisitReportBCSHerdAnalysisDto;
import com.app.cargill.dto.VisitReportBodyConditionScoreDto;
import com.app.cargill.dto.VisitReportColumnValueDto;
import com.app.cargill.dto.VisitReportDto;
import com.app.cargill.dto.VisitReportForageAuditDto;
import com.app.cargill.dto.VisitReportForageInventoriesDto;
import com.app.cargill.dto.VisitReportForagePennStateDto;
import com.app.cargill.dto.VisitReportHeatStressEvaluationDto;
import com.app.cargill.dto.VisitReportLocomotionHerdAnalysisDto;
import com.app.cargill.dto.VisitReportLocomotionScoreDto;
import com.app.cargill.dto.VisitReportMetabolicIncidenceDto;
import com.app.cargill.dto.VisitReportMilkSoldEvaluationDto;
import com.app.cargill.dto.VisitReportPenTimeBudgetDto;
import com.app.cargill.dto.VisitReportPenTimeBudgetPenAnalysisDto;
import com.app.cargill.dto.VisitReportRHCCHerdAnalysisDto;
import com.app.cargill.dto.VisitReportRHCCPenAnalysisDto;
import com.app.cargill.dto.VisitReportRoboticMilkEvaluationDto;
import com.app.cargill.dto.VisitReportRumenFillDto;
import com.app.cargill.dto.VisitReportRumenFillHerdAnalysisDto;
import com.app.cargill.dto.VisitReportRumenFillPenAnalysisDto;
import com.app.cargill.dto.VisitReportRumenHealthCudChewingDto;
import com.app.cargill.dto.VisitReportRumenHealthMSHerdAnalysisDto;
import com.app.cargill.dto.VisitReportRumenHealthManureScoreDto;
import com.app.cargill.dto.VisitReportRumenHealthManureScreeningDto;
import com.app.cargill.dto.VisitReportRumenHealthManureScreeningPenAnalysisDto;
import com.app.cargill.dto.VisitReportRumenHealthTMRParticleScoreDto;
import com.app.cargill.dto.VisitReportRumenHealthTMRParticleScoreHerdAnalysisDto;
import com.app.cargill.dto.VisitReportScorecardDto;
import com.app.cargill.dto.XAndYAxisValueDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.service.IUserService;
import com.app.cargill.service.impl.BCSHerdAnalysisReportServiceImpl;
import com.app.cargill.service.impl.BCSPenAnalysisReportServiceImpl;
import com.app.cargill.service.impl.BcsAnimalAnalysisReportServiceImpl;
import com.app.cargill.service.impl.CudChewingHerdAnalysisReportServiceImpl;
import com.app.cargill.service.impl.CudChewingPenAnalysisReportServiceImpl;
import com.app.cargill.service.impl.ForagePennStateHerdAnalysisReportServiceImpl;
import com.app.cargill.service.impl.LocomotionScoreAnimalAnalysisExcelReportServiceImpl;
import com.app.cargill.service.impl.LocomotionScoreHerdAnalysisReportServiceImpl;
import com.app.cargill.service.impl.LocomotionScorePenAnalysisReportServiceImpl;
import com.app.cargill.service.impl.MetabolicIncidenceReportServiceImpl;
import com.app.cargill.service.impl.MilkSoldEvaluationReportServiceImpl;
import com.app.cargill.service.impl.RoboticMilkEvaluationReportServiceImpl;
import com.app.cargill.service.impl.RumenFillHealthHerdAnalysisReportServiceImpl;
import com.app.cargill.service.impl.RumenFillHealthPenAnalysisReportServiceImpl;
import com.app.cargill.service.impl.RumenHealthMSHerdAnalysisReportServiceImpl;
import com.app.cargill.service.impl.RumenHealthMSPenAnalysisReportServiceImpl;
import com.app.cargill.service.impl.RumenHealthTMRParticleScoreHerdAnalysisReportServiceImpl;
import com.app.cargill.service.impl.RumenHealthTMRParticleScorePenAnalysisReportServiceImpl;
import com.app.cargill.service.impl.VisitReportServiceImpl;
import com.app.cargill.utils.ExcelUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.UUID;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.xddf.usermodel.PresetColor;
import org.bouncycastle.util.Strings;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
public class ReportControllerTest {
  @Mock ModelMapper modelMapper;
  @Mock PlayWrightComponent playWrightComponent;
  @Mock FreeMarkerComponent freeMarkerComponent;
  @Mock private IExcelReportService excelReportServiceImpl;
  @Mock private BeanFactory beanFactory;
  @Mock private static ResourceBundleMessageSource resourceBundleMessageSource;
  @Mock private ExcelUtils excelUtils;
  @Mock private IUserService userService;
  @Mock VisitReportServiceImpl visitReportServiceImpl;
  @InjectMocks private ReportsController controller;

  @BeforeEach
  void init() {
    ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
    messageSource.setDefaultEncoding("UTF-8");
    messageSource.setCacheSeconds(-1);
    messageSource.setBasename("internationalization/messages");
    resourceBundleMessageSource = messageSource;
  }

  @Test
  void downloadBcsHerdAnalysisReport() throws IOException, URISyntaxException {
    BCSHerdAnalysisReportServiceImpl mockSomeClass = mock(BCSHerdAnalysisReportServiceImpl.class);
    when(beanFactory.getBean(BCSHerdAnalysisReportServiceImpl.class)).thenReturn(mockSomeClass);
    lenient().when(modelMapper.map(any(), any())).thenReturn(prepareDataForBcsHerdAnalysis());
    excelReportServiceImpl = new BCSHerdAnalysisReportServiceImpl(modelMapper, freeMarkerComponent);
    // excel
    ResponseEntity<ByteArrayResource> result =
        controller.exportToExcel(
            prepareDataForBcsHerdAnalysis(),
            ReportsToBeanMappings.BCS_HERD_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
    // image
    result =
        controller.exportToImage(
            prepareDataForBcsHerdAnalysis(),
            ReportsToBeanMappings.BCS_HERD_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
  }

  @Test
  void downloadLocomotionScorePenAnalysisReport() throws IOException, URISyntaxException {
    LocomotionScorePenAnalysisReportDto build =
        LocomotionScorePenAnalysisReportDto.builder().categories(new ArrayList<>()).build();
    build.getCategories().add(new LocomotionScorePenAnalysisCategoryDto());
    Assertions.assertNotNull(build);

    LocomotionScorePenAnalysisReportServiceImpl mockSomeClass =
        mock(LocomotionScorePenAnalysisReportServiceImpl.class);
    when(beanFactory.getBean(LocomotionScorePenAnalysisReportServiceImpl.class))
        .thenReturn(mockSomeClass);
    lenient()
        .when(modelMapper.map(any(), any()))
        .thenReturn(prepareDataForLocomotionScorePenAnalysis());
    excelReportServiceImpl =
        new LocomotionScorePenAnalysisReportServiceImpl(modelMapper, freeMarkerComponent);
    ResponseEntity<ByteArrayResource> result =
        controller.exportToExcel(
            prepareDataForLocomotionScorePenAnalysis(),
            ReportsToBeanMappings.LOCOMOTION_SCORE_PEN_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
    // image
    result =
        controller.exportToImage(
            prepareDataForLocomotionScorePenAnalysis(),
            ReportsToBeanMappings.LOCOMOTION_SCORE_PEN_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
  }

  @Test
  void downloadBCSPenAnalysisReport() throws IOException, URISyntaxException {
    BCSPenAnalysisReportDto build1 =
        BCSPenAnalysisReportDto.builder().categories(new ArrayList<>()).build();
    build1.getCategories().add(new BCSPenAnalysisCategoryDto());
    Assertions.assertNotNull(build1);
    BCSPenAnalysisReportServiceImpl mockSomeClass = mock(BCSPenAnalysisReportServiceImpl.class);
    when(beanFactory.getBean(BCSPenAnalysisReportServiceImpl.class)).thenReturn(mockSomeClass);
    lenient().when(modelMapper.map(any(), any())).thenReturn(prepareDataForBCSPenAnalysis());
    excelReportServiceImpl = new BCSPenAnalysisReportServiceImpl(modelMapper, freeMarkerComponent);
    ResponseEntity<ByteArrayResource> result =
        controller.exportToExcel(
            prepareDataForBCSPenAnalysis(),
            ReportsToBeanMappings.BCS_PEN_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
    // image
    result =
        controller.exportToImage(
            prepareDataForBCSPenAnalysis(),
            ReportsToBeanMappings.BCS_PEN_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
  }

  @Test
  void downloadBCSAnimalAnalysisReport() throws IOException, URISyntaxException {
    BcsAnimalAnalysisReportDto build =
        BcsAnimalAnalysisReportDto.builder().animalAnalysis(new ArrayList<>()).build();
    build.getAnimalAnalysis().add(new BCSAnimalAnalysisRecordDto());
    Assertions.assertNotNull(build);

    BcsAnimalAnalysisReportServiceImpl mockSomeClass =
        mock(BcsAnimalAnalysisReportServiceImpl.class);
    when(beanFactory.getBean(BcsAnimalAnalysisReportServiceImpl.class)).thenReturn(mockSomeClass);
    lenient().when(modelMapper.map(any(), any())).thenReturn(prepareDataForBcsAnimalAnalysis());
    excelReportServiceImpl =
        new BcsAnimalAnalysisReportServiceImpl(modelMapper, freeMarkerComponent);
    ResponseEntity<ByteArrayResource> result =
        controller.exportToExcel(
            prepareDataForBcsAnimalAnalysis(),
            ReportsToBeanMappings.BCS_ANIMAL_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
    // image
    result =
        controller.exportToImage(
            prepareDataForBcsAnimalAnalysis(),
            ReportsToBeanMappings.BCS_ANIMAL_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
  }

  @Test
  void downloadCudChewingPenAnalysisReport() throws IOException, URISyntaxException {
    CudChewingPenAnalysisReportDto build =
        CudChewingPenAnalysisReportDto.builder().chewingPercentages(new ArrayList<>()).build();
    build.getChewingPercentages().add(new CudChewingPenAnalysisChewingDto());
    Assertions.assertNotNull(build);
    CudChewingPenAnalysisReportServiceImpl mockSomeClass =
        mock(CudChewingPenAnalysisReportServiceImpl.class);
    when(beanFactory.getBean(CudChewingPenAnalysisReportServiceImpl.class))
        .thenReturn(mockSomeClass);
    lenient().when(modelMapper.map(any(), any())).thenReturn(prepareDataForCudChewingPenAnalysis());
    excelReportServiceImpl =
        new CudChewingPenAnalysisReportServiceImpl(modelMapper, freeMarkerComponent);
    ResponseEntity<ByteArrayResource> result =
        controller.exportToExcel(
            prepareDataForBcsHerdAnalysis(),
            ReportsToBeanMappings.CUD_CHEWING_PEN_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
    // image
    result =
        controller.exportToImage(
            prepareDataForBcsHerdAnalysis(),
            ReportsToBeanMappings.CUD_CHEWING_PEN_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
  }

  @Test
  void downloadCudChewingHerdAnalysisReport() throws IOException, URISyntaxException {
    CudChewingHerdAnalysisReportServiceImpl mockSomeClass =
        mock(CudChewingHerdAnalysisReportServiceImpl.class);
    when(beanFactory.getBean(CudChewingHerdAnalysisReportServiceImpl.class))
        .thenReturn(mockSomeClass);
    lenient()
        .when(modelMapper.map(any(), any()))
        .thenReturn(prepareDataForCudChewingHerdAnalysis());
    excelReportServiceImpl =
        new CudChewingHerdAnalysisReportServiceImpl(modelMapper, freeMarkerComponent);
    ResponseEntity<ByteArrayResource> result =
        controller.exportToImage(
            prepareDataForBcsHerdAnalysis(),
            ReportsToBeanMappings.CUD_CHEWING_HERD_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
    // image
    result =
        controller.exportToExcel(
            prepareDataForBcsHerdAnalysis(),
            ReportsToBeanMappings.CUD_CHEWING_HERD_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
  }

  @Test
  void downloadLocomotionScoreAnimalAnalysisReport() throws IOException, URISyntaxException {
    LocomotionAnimalAnalysisReportDto build =
        LocomotionAnimalAnalysisReportDto.builder().animalAnalysis(new ArrayList<>()).build();
    build.getAnimalAnalysis().add(new LocomotionAnimalAnalysisRecordDto());
    Assertions.assertNotNull(build);
    LocomotionScoreAnimalAnalysisExcelReportServiceImpl mockSomeClass =
        mock(LocomotionScoreAnimalAnalysisExcelReportServiceImpl.class);
    when(beanFactory.getBean(LocomotionScoreAnimalAnalysisExcelReportServiceImpl.class))
        .thenReturn(mockSomeClass);
    lenient()
        .when(modelMapper.map(any(), any()))
        .thenReturn(prepareDataForLocomotionAnimalAnalysis());
    excelReportServiceImpl =
        new LocomotionScoreAnimalAnalysisExcelReportServiceImpl(modelMapper, freeMarkerComponent);
    ResponseEntity<ByteArrayResource> result =
        controller.exportToExcel(
            prepareDataForLocomotionAnimalAnalysis(),
            ReportsToBeanMappings.LOCOMOTION_SCORE_ANIMAL_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
    // image
    result =
        controller.exportToImage(
            prepareDataForLocomotionAnimalAnalysis(),
            ReportsToBeanMappings.LOCOMOTION_SCORE_ANIMAL_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
  }

  @Test
  void downloadLocomotionScoreHerdAnalysisReport() throws IOException, URISyntaxException {
    LocomotionScoreHerdAnalysisReportServiceImpl mockSomeClass =
        mock(LocomotionScoreHerdAnalysisReportServiceImpl.class);
    when(beanFactory.getBean(LocomotionScoreHerdAnalysisReportServiceImpl.class))
        .thenReturn(mockSomeClass);
    lenient()
        .when(modelMapper.map(any(), any()))
        .thenReturn(prepareDataForLocomotionScoreHerdAnalysis());
    excelReportServiceImpl =
        new LocomotionScoreHerdAnalysisReportServiceImpl(modelMapper, freeMarkerComponent);
    ResponseEntity<ByteArrayResource> result =
        controller.exportToExcel(
            prepareDataForLocomotionScoreHerdAnalysis(),
            ReportsToBeanMappings.LOCOMOTION_SCORE_HERD_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
    // image
    result =
        controller.exportToImage(
            prepareDataForLocomotionScoreHerdAnalysis(),
            ReportsToBeanMappings.LOCOMOTION_SCORE_HERD_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
  }

  @Test
  void downloadRumenHealthManureScorePenAnalysisReport() throws IOException, URISyntaxException {
    RumenHealthMSPenAnalysisReportDto build1 =
        RumenHealthMSPenAnalysisReportDto.builder().categories(new ArrayList<>()).build();
    build1.getCategories().add(new RumenHealthMSPenAnalysisCategoryDto());
    Assertions.assertNotNull(build1);
    RumenHealthMSPenAnalysisReportServiceImpl mockSomeClass =
        mock(RumenHealthMSPenAnalysisReportServiceImpl.class);
    when(beanFactory.getBean(RumenHealthMSPenAnalysisReportServiceImpl.class))
        .thenReturn(mockSomeClass);
    lenient()
        .when(modelMapper.map(any(), any()))
        .thenReturn(prepareDataForRumenHealthManureScorePenAnalysis());
    excelReportServiceImpl =
        new RumenHealthMSPenAnalysisReportServiceImpl(modelMapper, freeMarkerComponent);
    ResponseEntity<ByteArrayResource> result =
        controller.exportToExcel(
            prepareDataForRumenHealthManureScorePenAnalysis(),
            ReportsToBeanMappings.RUMEN_HEALTH_MS_PEN_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
    // image
    result =
        controller.exportToImage(
            prepareDataForRumenHealthManureScorePenAnalysis(),
            ReportsToBeanMappings.RUMEN_HEALTH_MS_PEN_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
  }

  @Test
  void downloadRumenHealthMSHerdAnalysisReport() throws IOException, URISyntaxException {
    RumenHealthMSHerdAnalysisReportServiceImpl mockSomeClass =
        mock(RumenHealthMSHerdAnalysisReportServiceImpl.class);
    when(beanFactory.getBean(RumenHealthMSHerdAnalysisReportServiceImpl.class))
        .thenReturn(mockSomeClass);
    lenient()
        .when(modelMapper.map(any(), any()))
        .thenReturn(prepareDataForRumenHealthMSHerdAnalysis());
    excelReportServiceImpl =
        new RumenHealthMSHerdAnalysisReportServiceImpl(modelMapper, freeMarkerComponent);
    // excel
    ResponseEntity<ByteArrayResource> result =
        controller.exportToExcel(
            prepareDataForRumenHealthMSHerdAnalysis(),
            ReportsToBeanMappings.RUMEN_HEALTH_MS_HERD_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
    // image
    result =
        controller.exportToImage(
            prepareDataForRumenHealthMSHerdAnalysis(),
            ReportsToBeanMappings.RUMEN_HEALTH_MS_HERD_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
  }

  @Test
  void downloadMilkSoldEvaluationReport() throws IOException, URISyntaxException {
    MilkSoldEvaluationReportServiceImpl mockSomeClass =
        mock(MilkSoldEvaluationReportServiceImpl.class);
    when(beanFactory.getBean(MilkSoldEvaluationReportServiceImpl.class)).thenReturn(mockSomeClass);
    lenient().when(modelMapper.map(any(), any())).thenReturn(prepareDataForMilkSoldEvaluation());
    excelReportServiceImpl =
        new MilkSoldEvaluationReportServiceImpl(modelMapper, freeMarkerComponent);
    // excel
    ResponseEntity<ByteArrayResource> result =
        controller.exportToExcel(
            prepareDataForMilkSoldEvaluation(),
            ReportsToBeanMappings.MILK_SOLD_EVALUATION_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
    // image
    result =
        controller.exportToImage(
            prepareDataForMilkSoldEvaluation(),
            ReportsToBeanMappings.MILK_SOLD_EVALUATION_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
  }

  @Test
  void downloadMilkSoldEvaluationReportForMilkProduction() throws IOException, URISyntaxException {
    MilkSoldEvaluationReportServiceImpl mockSomeClass =
        mock(MilkSoldEvaluationReportServiceImpl.class);
    when(beanFactory.getBean(MilkSoldEvaluationReportServiceImpl.class)).thenReturn(mockSomeClass);
    lenient()
        .when(modelMapper.map(any(), any()))
        .thenReturn(prepareDataForMilkSoldEvaluationMilkProductionAndDim());
    excelReportServiceImpl =
        new MilkSoldEvaluationReportServiceImpl(modelMapper, freeMarkerComponent);
    // excel
    ResponseEntity<ByteArrayResource> result =
        controller.exportToExcel(
            prepareDataForMilkSoldEvaluationMilkProductionAndDim(),
            ReportsToBeanMappings.MILK_SOLD_EVALUATION_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
    // image
    result =
        controller.exportToImage(
            prepareDataForMilkSoldEvaluationMilkProductionAndDim(),
            ReportsToBeanMappings.MILK_SOLD_EVALUATION_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
  }

  @Test
  void downloadMilkSoldEvaluationReportForMilkFatAndProteinPercent()
      throws IOException, URISyntaxException {
    MilkSoldEvaluationReportServiceImpl mockSomeClass =
        mock(MilkSoldEvaluationReportServiceImpl.class);
    when(beanFactory.getBean(MilkSoldEvaluationReportServiceImpl.class)).thenReturn(mockSomeClass);
    lenient()
        .when(modelMapper.map(any(), any()))
        .thenReturn(prepareDataForMilkSoldEvaluationMilkProteinPercent());
    excelReportServiceImpl =
        new MilkSoldEvaluationReportServiceImpl(modelMapper, freeMarkerComponent);
    // excel
    ResponseEntity<ByteArrayResource> result =
        controller.exportToExcel(
            prepareDataForMilkSoldEvaluationMilkProteinPercent(),
            ReportsToBeanMappings.MILK_SOLD_EVALUATION_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
    // image
    result =
        controller.exportToImage(
            prepareDataForMilkSoldEvaluationMilkProteinPercent(),
            ReportsToBeanMappings.MILK_SOLD_EVALUATION_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
  }

  @Test
  void downloadRoboticMilkingEvaluationReport() throws IOException, URISyntaxException {
    RoboticMilkEvaluationReportServiceImpl mockSomeClass =
        mock(RoboticMilkEvaluationReportServiceImpl.class);
    when(beanFactory.getBean(RoboticMilkEvaluationReportServiceImpl.class))
        .thenReturn(mockSomeClass);
    lenient().when(modelMapper.map(any(), any())).thenReturn(prepareDataForRoboticMilkEvaluation());
    excelReportServiceImpl =
        new RoboticMilkEvaluationReportServiceImpl(modelMapper, freeMarkerComponent);
    // excel
    ResponseEntity<ByteArrayResource> result =
        controller.exportToExcel(
            prepareDataForRoboticMilkEvaluation(),
            ReportsToBeanMappings.ROBOTIC_MILK_EVALUATION_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
    // image
    result =
        controller.exportToImage(
            prepareDataForRoboticMilkEvaluation(),
            ReportsToBeanMappings.ROBOTIC_MILK_EVALUATION_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
  }

  @Test
  void downloadMetabolicIncidenceReport() throws IOException, URISyntaxException {
    MetabolicIncidenceReportServiceImpl mockSomeClass =
        mock(MetabolicIncidenceReportServiceImpl.class);
    when(beanFactory.getBean(MetabolicIncidenceReportServiceImpl.class)).thenReturn(mockSomeClass);
    lenient().when(modelMapper.map(any(), any())).thenReturn(prepareDataForMetabolicIncidence());
    excelReportServiceImpl =
        new MetabolicIncidenceReportServiceImpl(modelMapper, freeMarkerComponent);
    // excel
    ResponseEntity<ByteArrayResource> result =
        controller.exportToExcel(
            prepareDataForMetabolicIncidence(),
            ReportsToBeanMappings.METABOLIC_INCIDENCE_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
    // image
    result =
        controller.exportToImage(
            prepareDataForMetabolicIncidence(),
            ReportsToBeanMappings.METABOLIC_INCIDENCE_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
  }

  @Test
  void downloadRumenFillHealthPenAnalysisReport() throws IOException, URISyntaxException {
    RumenFillHealthPenAnalysisReportDto build1 =
        RumenFillHealthPenAnalysisReportDto.builder().categories(new ArrayList<>()).build();
    build1.getCategories().add(new RumenFillHealthPenAnalysisCategoryDto());
    Assertions.assertNotNull(build1);
    RumenFillHealthPenAnalysisReportServiceImpl mockSomeClass =
        mock(RumenFillHealthPenAnalysisReportServiceImpl.class);
    when(beanFactory.getBean(RumenFillHealthPenAnalysisReportServiceImpl.class))
        .thenReturn(mockSomeClass);
    lenient()
        .when(modelMapper.map(any(), any()))
        .thenReturn(prepareDataForRumenFillHealthPenAnalysis());
    excelReportServiceImpl =
        new RumenFillHealthPenAnalysisReportServiceImpl(modelMapper, freeMarkerComponent);
    ResponseEntity<ByteArrayResource> result =
        controller.exportToExcel(
            prepareDataForRumenFillHealthPenAnalysis(),
            ReportsToBeanMappings.RUMEN_FILL_HEALTH_PEN_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
    // image
    result =
        controller.exportToImage(
            prepareDataForRumenFillHealthPenAnalysis(),
            ReportsToBeanMappings.RUMEN_FILL_HEALTH_PEN_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
  }

  @Test
  void downloadRumenFillHealthHerdAnalysisReport() throws IOException, URISyntaxException {
    RumenFillHealthHerdAnalysisReportServiceImpl mockSomeClass =
        mock(RumenFillHealthHerdAnalysisReportServiceImpl.class);
    when(beanFactory.getBean(RumenFillHealthHerdAnalysisReportServiceImpl.class))
        .thenReturn(mockSomeClass);
    lenient()
        .when(modelMapper.map(any(), any()))
        .thenReturn(prepareDataForRumenFillHealthHerdAnalysis());
    excelReportServiceImpl =
        new RumenFillHealthHerdAnalysisReportServiceImpl(modelMapper, freeMarkerComponent);
    // excel
    ResponseEntity<ByteArrayResource> result =
        controller.exportToExcel(
            prepareDataForRumenFillHealthHerdAnalysis(),
            ReportsToBeanMappings.RUMEN_FILL_HEALTH_HERD_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
    // image
    result =
        controller.exportToImage(
            prepareDataForRumenFillHealthHerdAnalysis(),
            ReportsToBeanMappings.RUMEN_FILL_HEALTH_HERD_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
  }

  @Test
  void downloadForagePennStateHerdAnalysisReport() throws IOException, URISyntaxException {
    ForagePennStateHerdAnalysisReportServiceImpl mockSomeClass =
        mock(ForagePennStateHerdAnalysisReportServiceImpl.class);
    when(beanFactory.getBean(ForagePennStateHerdAnalysisReportServiceImpl.class))
        .thenReturn(mockSomeClass);
    lenient()
        .when(modelMapper.map(any(), any()))
        .thenReturn(prepareDataForForagePennStateHerdAnalysis());
    excelReportServiceImpl =
        new ForagePennStateHerdAnalysisReportServiceImpl(modelMapper, freeMarkerComponent);
    // excel
    ResponseEntity<ByteArrayResource> result =
        controller.exportToExcel(
            prepareDataForForagePennStateHerdAnalysis(),
            ReportsToBeanMappings.FORAGE_PENN_STATE_HERD_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
    // image
    result =
        controller.exportToImage(
            prepareDataForForagePennStateHerdAnalysis(),
            ReportsToBeanMappings.FORAGE_PENN_STATE_HERD_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
  }

  @Test
  void downloadRumenHealthTmrParticlePenAnalysisReport() throws IOException, URISyntaxException {
    RumenHealthTMRParticleScorePenAnalysisReportServiceImpl mockSomeClass =
        mock(RumenHealthTMRParticleScorePenAnalysisReportServiceImpl.class);
    when(beanFactory.getBean(RumenHealthTMRParticleScorePenAnalysisReportServiceImpl.class))
        .thenReturn(mockSomeClass);
    lenient()
        .when(modelMapper.map(any(), any()))
        .thenReturn(prepareDataForRumenHealthTmrParticleScorePenAnalysis());
    excelReportServiceImpl =
        new RumenHealthTMRParticleScorePenAnalysisReportServiceImpl(
            modelMapper, freeMarkerComponent);
    // excel
    ResponseEntity<ByteArrayResource> result =
        controller.exportToExcel(
            prepareDataForRumenHealthTmrParticleScorePenAnalysis(),
            ReportsToBeanMappings.RUMEN_HEALTH_TMR_PARTICLE_SCORE_PEN_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
    // image
    result =
        controller.exportToImage(
            prepareDataForRumenHealthTmrParticleScorePenAnalysis(),
            ReportsToBeanMappings.RUMEN_HEALTH_TMR_PARTICLE_SCORE_PEN_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
  }

  @Test
  void downloadRumenHealthTmrParticleHerdAnalysisReport() throws IOException, URISyntaxException {
    RumenHealthTMRParticleScoreHerdAnalysisReportServiceImpl mockSomeClass =
        mock(RumenHealthTMRParticleScoreHerdAnalysisReportServiceImpl.class);
    when(beanFactory.getBean(RumenHealthTMRParticleScoreHerdAnalysisReportServiceImpl.class))
        .thenReturn(mockSomeClass);
    lenient()
        .when(modelMapper.map(any(), any()))
        .thenReturn(prepareDataForRumenHealthTmrParticleScoreHerdAnalysis());
    excelReportServiceImpl =
        new RumenHealthTMRParticleScoreHerdAnalysisReportServiceImpl(
            modelMapper, freeMarkerComponent);
    // excel
    ResponseEntity<ByteArrayResource> result =
        controller.exportToExcel(
            prepareDataForRumenHealthTmrParticleScoreHerdAnalysis(),
            ReportsToBeanMappings.RUMEN_HEALTH_TMR_PARTICLE_SCORE_HERD_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
    // image
    result =
        controller.exportToImage(
            prepareDataForRumenHealthTmrParticleScoreHerdAnalysis(),
            ReportsToBeanMappings.RUMEN_HEALTH_TMR_PARTICLE_SCORE_HERD_ANALYSIS_REPORT,
            LangCodes.EN.name());
    assertEquals(HttpStatus.CREATED, result.getStatusCode());
  }

  @Test
  void downloadVisitReportReport() throws IOException {
    when(visitReportServiceImpl.downloadVisitReport(any(), any(), any()))
        .thenReturn(new ByteArrayResource(Strings.toByteArray("")));

    ResponseEntity<?> result =
        controller.visitReport(
            ReportControllerTest.prepareDataForVisitReport(), Locale.ENGLISH.toString());

    Object body = result.getBody();
    assertNotNull(body);

    result = controller.visitReport(VisitReportDto.builder().build(), Locale.ENGLISH.toString());

    body = result.getBody();
    assertNotNull(body);
  }

  @Test
  void whenShareVisitReportOfflineIsCalledCorrectResponseIsReturned()
      throws IOException, NotFoundDEException, AlreadyExistsDEException, CustomDEExceptions {

    ResponseEntity<?> result =
        controller.shareVisitReportToSharePoint(UUID.randomUUID().toString(), null);

    assertEquals(HttpStatus.ACCEPTED, result.getStatusCode());
  }

  public static RoboticMilkEvaluationReportDto prepareDataForRoboticMilkEvaluation() {
    RoboticMilkEvaluationReportDto build =
        RoboticMilkEvaluationReportDto.builder()
            .visitName("test visit")
            .visitDate("12/12")
            .fileName("Robotic Milk Evaluation.xlsx")
            .toolName("Robotic Milk Evaluation")
            .categoryLabel("AMS Utilization")
            .build();
    build.setDualYaxisGraph(
        DualYAxisGraphDto.builder()
            .sheetName("test sheet")
            .yleftLabel("y Left label")
            .yrightLabel("y right label")
            .yrightLineColor(PresetColor.GREEN)
            .yleftLineColor(PresetColor.GREEN)
            .yleftDataPoints(List.of(new XAndYAxisValueDto("21/21", 30.0)))
            .yrightDataPoints(List.of(new XAndYAxisValueDto("21/21", 30.0)))
            .build());
    build.setSingleYaxisGraph(
        SingleYAxisGraphDto.builder()
            .sheetName("test sheet 1")
            .yleftLabel("y Left label")
            .yleftLineColor(PresetColor.GREEN)
            .yleftDataPoints(List.of(new XAndYAxisValueDto("21/21", 30.0)))
            .build());
    return build;
  }

  public static MilkSoldEvaluationReportDto prepareDataForMilkSoldEvaluation() {
    MilkSoldEvaluationReportDto build =
        MilkSoldEvaluationReportDto.builder()
            .visitName("lorem")
            .visitDate("21-12-22")
            .toolName("Milk Sold Evaluation")
            .fileName("LocomotionPenAnalysisExcelReport.xlsx")
            .yaxisLeftLabel("lbs")
            .yaxisRightLabel("test")
            .chartType(MilkSoldEvaluationExportChartVariations.COMPONENT_YIELD_AND_EFFICIENCY)
            .build();
    build.setDataPoints(new ArrayList<>());
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/18", 3.65, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/19", 2.0, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/20", 2.2, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/21", 2.21, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/22", 2.12, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/23", 2.3, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/23", 3.65, 3.65));
    return build;
  }

  public static MilkSoldEvaluationReportDto prepareDataForMilkSoldEvaluationMilkProductionAndDim() {
    MilkSoldEvaluationReportDto build =
        MilkSoldEvaluationReportDto.builder()
            .visitName("lorem")
            .visitDate("21-12-22")
            .toolName("Milk Sold Evaluation")
            .fileName("LocomotionPenAnalysisExcelReport.xlsx")
            .yaxisLeftLabel("lbs")
            .yaxisRightLabel("test")
            .chartType(MilkSoldEvaluationExportChartVariations.MILK_PRODUCTION_AND_DIM)
            .build();
    build.setDataPoints(new ArrayList<>());
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/18", 3.65, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/19", 2.0, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/20", 2.2, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/21", 2.21, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/22", 2.12, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/23", 2.3, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/23", 3.65, 3.65));
    return build;
  }

  public static MilkSoldEvaluationReportDto prepareDataForMilkSoldEvaluationMilkProteinPercent() {
    MilkSoldEvaluationReportDto build =
        MilkSoldEvaluationReportDto.builder()
            .visitName("lorem")
            .visitDate("21-12-22")
            .toolName("Milk Sold Evaluation")
            .fileName("LocomotionPenAnalysisExcelReport.xlsx")
            .yaxisLeftLabel("lbs")
            .yaxisRightLabel("test")
            .chartType(
                MilkSoldEvaluationExportChartVariations.MILK_FAT_PERCENT_AND_MILK_PROTEIN_PERCENT)
            .build();
    build.setDataPoints(new ArrayList<>());
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/18", 3.65, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/19", 2.0, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/20", 2.2, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/21", 2.21, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/22", 2.12, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/23", 2.3, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/23", 3.65, 3.65));
    return build;
  }

  public static MilkSoldEvaluationReportDto prepareDataForMilkSoldEvaluationCellCountAndUrea() {
    MilkSoldEvaluationReportDto build =
        MilkSoldEvaluationReportDto.builder()
            .visitName("lorem")
            .visitDate("21-12-22")
            .toolName("Milk Sold Evaluation")
            .fileName("LocomotionPenAnalysisExcelReport.xlsx")
            .yaxisLeftLabel("lbs")
            .yaxisRightLabel("test")
            .chartType(MilkSoldEvaluationExportChartVariations.SOMATIC_CELL_COUNT_AND_MILK_UREA)
            .build();
    build.setDataPoints(new ArrayList<>());
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/18", 3.65, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/19", 2.0, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/20", 2.2, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/21", 2.21, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/22", 2.12, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/23", 2.3, 3.65));
    build.getDataPoints().add(new MilkSoldEvaluationExportChartDto("9/23", 3.65, 3.65));
    return build;
  }

  public static CudChewingPenAnalysisReportDto prepareDataForCudChewingPenAnalysis() {
    CudChewingPenAnalysisReportDto build =
        CudChewingPenAnalysisReportDto.builder()
            .visitName("lorem")
            .visitDate("21-12-22")
            .toolName("Pen Chewing")
            .analysisType("Herd Analysis")
            .fileName("CudChewingPenAnalysisAnalysis.xlsx")
            .penName("pen 1")
            .isNoOfChews(true)
            .standardDeviation(10.0)
            .build();
    build.setChewingPercentages(new ArrayList<>());
    build.getChewingPercentages().add(new CudChewingPenAnalysisChewingDto("9/18", 3.65, 3.65));
    build.getChewingPercentages().add(new CudChewingPenAnalysisChewingDto("9/19", 2.0, 3.65));
    build.getChewingPercentages().add(new CudChewingPenAnalysisChewingDto("9/20", 2.2, 3.65));
    build.getChewingPercentages().add(new CudChewingPenAnalysisChewingDto("9/21", 2.21, 3.65));
    build.getChewingPercentages().add(new CudChewingPenAnalysisChewingDto("9/22", 2.12, 3.65));
    build.getChewingPercentages().add(new CudChewingPenAnalysisChewingDto("9/23", 2.3, 3.65));
    build.getChewingPercentages().add(new CudChewingPenAnalysisChewingDto("9/23", null, null));
    return build;
  }

  public static LocomotionScoreHerdAnalysisReportDto prepareDataForLocomotionScoreHerdAnalysis() {
    LocomotionScoreHerdAnalysisReportDto build =
        LocomotionScoreHerdAnalysisReportDto.builder()
            .visitName("lorem")
            .visitDate("21-12-22")
            .toolName("Locomotion Score")
            .analysisType("Herd Analysis")
            .standardDeviation(12.36)
            .average(96.3)
            .fileName("LocomotionScoreHerdAnalysisReport.xlsx")
            .build();
    build.setHerdAvg(new LinkedHashMap<>());
    build.getHerdAvg().put("1.0", 40.32);
    build.getHerdAvg().put("2.0", 60.2);
    build.getHerdAvg().put("3.0", 70.0);
    build.getHerdAvg().put("4.0", 88.6);
    build.getHerdAvg().put("5.0", 66.6);

    build.setGoals(new LinkedHashMap<>());
    build.getGoals().put("1.0", 20.32);
    build.getGoals().put("2.0", 30.2);
    build.getGoals().put("3.0", 10.0);
    build.getGoals().put("4.0", 15.6);
    build.getGoals().put("5.0", 35.6);

    return build;
  }

  public static LocomotionScorePenAnalysisReportDto prepareDataForLocomotionScorePenAnalysis() {
    LocomotionScorePenAnalysisReportDto build =
        LocomotionScorePenAnalysisReportDto.builder()
            .visitName("lorem")
            .visitDate("21-12-22")
            .toolName("Locomotion Score")
            .analysisType("Pen Analysis")
            .fileName("LocomotionPenAnalysisExcelReport.xlsx")
            .average(12.36)
            .standardDeviation(63.5)
            .penName("pen 1")
            .build();
    build.setCategories(new ArrayList<>());
    build.getCategories().add(new LocomotionScorePenAnalysisCategoryDto("9/18", 3.65));
    build.getCategories().add(new LocomotionScorePenAnalysisCategoryDto("9/19", 2.0));
    build.getCategories().add(new LocomotionScorePenAnalysisCategoryDto("9/20", 2.2));
    build.getCategories().add(new LocomotionScorePenAnalysisCategoryDto("9/21", 2.21));
    build.getCategories().add(new LocomotionScorePenAnalysisCategoryDto("9/22", 2.12));
    build.getCategories().add(new LocomotionScorePenAnalysisCategoryDto("9/23", 2.3));
    build.getCategories().add(new LocomotionScorePenAnalysisCategoryDto("9/23", null));
    return build;
  }

  public static BCSToolHeardAnalysisReportDto prepareDataForBcsHerdAnalysis() {
    BCSToolHeardAnalysisReportDto build =
        BCSToolHeardAnalysisReportDto.builder()
            .lactationStages(
                Arrays.stream(LactationStage.values()).map(Enum::name).toArray(String[]::new))
            .milkHdDay(new LinkedHashMap<>())
            .bcsAverage(new LinkedHashMap<>())
            .bcsMin(new LinkedHashMap<>())
            .bcsMax(new LinkedHashMap<>())
            // header details
            .visitName("lorem")
            .visitDate("21-12-22")
            .toolName("BCS")
            .analysisType("Heard Analysis")
            .fileName("BCSHeardAnalysis.xlsx")
            .build();
    // milk hd days
    build.getMilkHdDay().put(LactationStage.Fresh.name(), 70.0);
    build.getMilkHdDay().put(LactationStage.EarlyLactation.name(), 90.0);
    build.getMilkHdDay().put(LactationStage.PeakMilk.name(), 95.0);
    build.getMilkHdDay().put(LactationStage.MidLactation.name(), 78.0);
    build.getMilkHdDay().put(LactationStage.LateLactation.name(), 55.0);

    // bcs average
    build.getBcsAverage().put(LactationStage.FarOffDry.name(), 3.55);
    build.getBcsAverage().put(LactationStage.CloseUpDry.name(), 3.50);
    build.getBcsAverage().put(LactationStage.Fresh.name(), 3.14);
    build.getBcsAverage().put(LactationStage.EarlyLactation.name(), 2.94);
    build.getBcsAverage().put(LactationStage.PeakMilk.name(), 3.01);
    build.getBcsAverage().put(LactationStage.MidLactation.name(), 3.24);
    build.getBcsAverage().put(LactationStage.LateLactation.name(), 3.39);

    // bcs min
    build.getBcsMin().put(LactationStage.FarOffDry.name(), 3.25);
    build.getBcsMin().put(LactationStage.CloseUpDry.name(), 3.25);
    build.getBcsMin().put(LactationStage.Fresh.name(), 2.75);
    build.getBcsMin().put(LactationStage.EarlyLactation.name(), 2.5);
    build.getBcsMin().put(LactationStage.PeakMilk.name(), 2.5);
    build.getBcsMin().put(LactationStage.MidLactation.name(), 2.75);
    build.getBcsMin().put(LactationStage.LateLactation.name(), 3.0);

    // bcs max
    build.getBcsMax().put(LactationStage.FarOffDry.name(), 3.75);
    build.getBcsMax().put(LactationStage.CloseUpDry.name(), 3.75);
    build.getBcsMax().put(LactationStage.Fresh.name(), 3.25);
    build.getBcsMax().put(LactationStage.EarlyLactation.name(), 3.0);
    build.getBcsMax().put(LactationStage.PeakMilk.name(), 3.0);
    build.getBcsMax().put(LactationStage.MidLactation.name(), 3.25);
    build.getBcsMax().put(LactationStage.LateLactation.name(), 3.75);
    return build;
  }

  public static ProfitabilityAnalysisReportDto prepareDataForProfitabilityAnalysis() {

    ProfitabilityAnalysisReportDto profitabilityAnalysisReportDto =
        ProfitabilityAnalysisReportDto.builder()
            .leftYAxis(List.of(XAndYAxisValueDto.builder().x("10-10-10").y(12.3).build()))
            .rightYAxis(List.of(XAndYAxisValueDto.builder().x("10-10-10").y(12.3).build()))
            .reportType(ReportsToBeanMappings.PROFITABILITY_ANALYSIS_TOTAL_PRODUCTION)
            .fileName("testfile.xlsx")
            .build();
    return profitabilityAnalysisReportDto;
  }

  public static ProfitabilityAnalysisReportDto
      prepareDataForProfitabilityAnalysisRevenuePerCowPerDay() {

    ProfitabilityAnalysisReportDto profitabilityAnalysisReportDto =
        ProfitabilityAnalysisReportDto.builder()
            .leftYAxis(List.of(XAndYAxisValueDto.builder().x("10-10-10").y(12.3).build()))
            .rightYAxis(List.of(XAndYAxisValueDto.builder().x("10-10-10").y(12.3).build()))
            .reportType(ReportsToBeanMappings.PROFITABILITY_ANALYSIS_REVENUE_PER_COW_PER_DAY)
            .currency("$")
            .fileName("testfile.xlsx")
            .build();
    return profitabilityAnalysisReportDto;
  }

  public static ProfitabilityAnalysisReportDto
      prepareDataForProfitabilityAnalysisMilkPriceFeedingCost() {

    ProfitabilityAnalysisReportDto profitabilityAnalysisReportDto =
        ProfitabilityAnalysisReportDto.builder()
            .leftYAxis(List.of(XAndYAxisValueDto.builder().x("10-10-10").y(12.3).build()))
            .rightYAxis(List.of(XAndYAxisValueDto.builder().x("10-10-10").y(12.3).build()))
            .currency("$")
            .reportType(ReportsToBeanMappings.PROFITABILITY_ANALYSIS_MILK_PRICE_FEEDING_COST)
            .fileName("testfile.xlsx")
            .build();
    return profitabilityAnalysisReportDto;
  }

  public static CudChewingHerdAnalysisReportDto prepareDataForCudChewingHerdAnalysis() {
    CudChewingHerdAnalysisReportDto build =
        CudChewingHerdAnalysisReportDto.builder()
            // header details
            .visitName("lorem")
            .visitDate("21-12-22")
            .toolName("Cud Chewing")
            .analysisType("Heard Analysis")
            .fileName("CudChewingHeardAnalysis.xlsx")
            .chewsPerRegurgitation(new LinkedHashMap<>())
            .goalChews(new LinkedHashMap<>())
            .cudChewingPercentage(new LinkedHashMap<>())
            .goalCudChewingPercentage(new LinkedHashMap<>())
            .build();

    // chewsPerRegurgitation
    build.getChewsPerRegurgitation().put(LactationStage.FarOffDry.name(), 58.0);
    build.getChewsPerRegurgitation().put(LactationStage.CloseUpDry.name(), 61.2);
    build.getChewsPerRegurgitation().put(LactationStage.Fresh.name(), 60.0);
    build.getChewsPerRegurgitation().put(LactationStage.EarlyLactation.name(), 61.0);
    build.getChewsPerRegurgitation().put(LactationStage.PeakMilk.name(), 59.0);
    build.getChewsPerRegurgitation().put(LactationStage.MidLactation.name(), 61.0);
    build.getChewsPerRegurgitation().put(LactationStage.LateLactation.name(), 60.0);

    // goalChews
    build.getGoalChews().put(LactationStage.FarOffDry.name(), 60.0);
    build.getGoalChews().put(LactationStage.CloseUpDry.name(), 60.0);
    build.getGoalChews().put(LactationStage.Fresh.name(), 60.0);
    build.getGoalChews().put(LactationStage.EarlyLactation.name(), 60.0);
    build.getGoalChews().put(LactationStage.PeakMilk.name(), 60.0);
    build.getGoalChews().put(LactationStage.MidLactation.name(), 60.0);
    build.getGoalChews().put(LactationStage.LateLactation.name(), 60.0);

    // cudChewingPercentage
    build.getCudChewingPercentage().put(LactationStage.FarOffDry.name(), 33.33);
    build.getCudChewingPercentage().put(LactationStage.CloseUpDry.name(), 50.0);
    build.getCudChewingPercentage().put(LactationStage.Fresh.name(), 70.0);
    build.getCudChewingPercentage().put(LactationStage.EarlyLactation.name(), 80.0);
    build.getCudChewingPercentage().put(LactationStage.PeakMilk.name(), 90.0);
    build.getCudChewingPercentage().put(LactationStage.MidLactation.name(), 100.0);
    build.getCudChewingPercentage().put(LactationStage.LateLactation.name(), 110.0);

    // goalCudChewingPercentage
    build.getGoalCudChewingPercentage().put(LactationStage.FarOffDry.name(), 60.0);
    build.getGoalCudChewingPercentage().put(LactationStage.CloseUpDry.name(), 60.0);
    build.getGoalCudChewingPercentage().put(LactationStage.Fresh.name(), 78.0);
    build.getGoalCudChewingPercentage().put(LactationStage.EarlyLactation.name(), 76.0);
    build.getGoalCudChewingPercentage().put(LactationStage.PeakMilk.name(), 90.0);
    build.getGoalCudChewingPercentage().put(LactationStage.MidLactation.name(), 60.0);
    build.getGoalCudChewingPercentage().put(LactationStage.LateLactation.name(), 78.0);
    return build;
  }

  public static BCSPenAnalysisReportDto prepareDataForBCSPenAnalysis() {
    BCSPenAnalysisReportDto build =
        BCSPenAnalysisReportDto.builder()
            .visitName("lorem")
            .visitDate("21-12-22")
            .toolName("Cud Chewing")
            .analysisType("Herd Analysis")
            .fileName("CudChewingPenAnalysisAnalysis.xlsx")
            .standardDeviation(12.02)
            .average(65.2)
            .penName("pen 1")
            .build();
    build.setCategories(new ArrayList<>());
    build.getCategories().add(new BCSPenAnalysisCategoryDto("9/18", 2.0));
    build.getCategories().add(new BCSPenAnalysisCategoryDto("9/19", 3.65));
    build.getCategories().add(new BCSPenAnalysisCategoryDto("9/20", 2.0));
    build.getCategories().add(new BCSPenAnalysisCategoryDto("9/21", 2.22));
    build.getCategories().add(new BCSPenAnalysisCategoryDto("9/22", 2.21));
    build.getCategories().add(new BCSPenAnalysisCategoryDto("9/23", 2.12));
    build.getCategories().add(new BCSPenAnalysisCategoryDto("9/23", 2.3));
    return build;
  }

  public static RumenHealthMSPenAnalysisReportDto
      prepareDataForRumenHealthManureScorePenAnalysis() {
    RumenHealthMSPenAnalysisReportDto build =
        RumenHealthMSPenAnalysisReportDto.builder()
            .visitName("lorem")
            .visitDate("21-12-22")
            .toolName("Rumen Health")
            .analysisType("Pen Analysis")
            .fileName("RumenHealthManureScorePenAnalysisAnalysis.xlsx")
            .standardDeviation(12.02)
            .average(65.2)
            .penName("pen 1")
            .build();
    build.setCategories(new ArrayList<>());
    build.getCategories().add(new RumenHealthMSPenAnalysisCategoryDto("9/18", 2.0));
    build.getCategories().add(new RumenHealthMSPenAnalysisCategoryDto("9/19", 3.65));
    build.getCategories().add(new RumenHealthMSPenAnalysisCategoryDto("9/20", 2.0));
    build.getCategories().add(new RumenHealthMSPenAnalysisCategoryDto("9/21", 2.22));
    build.getCategories().add(new RumenHealthMSPenAnalysisCategoryDto("9/22", 2.21));
    build.getCategories().add(new RumenHealthMSPenAnalysisCategoryDto("9/23", 2.12));
    build.getCategories().add(new RumenHealthMSPenAnalysisCategoryDto("9/23", 2.3));
    return build;
  }

  public static BcsAnimalAnalysisReportDto prepareDataForBcsAnimalAnalysis() {
    BcsAnimalAnalysisReportDto build =
        BcsAnimalAnalysisReportDto.builder()
            .fileName("animal analysis")
            .visitName("test visit")
            .visitDate("12/12")
            .toolName("bcs")
            .analysisType("animal analysis")
            .animalTagName("animal-1")
            .calvingDate("12/12")
            .animalAnalysis(new ArrayList<>())
            .build();
    build.getAnimalAnalysis().add(new BCSAnimalAnalysisRecordDto("7/4/2022", "Pen1", 14, 3.1, 1.0));
    build.getAnimalAnalysis().add(new BCSAnimalAnalysisRecordDto("8/4/2022", "Pen1", 47, 2.7, 1.0));
    build
        .getAnimalAnalysis()
        .add(new BCSAnimalAnalysisRecordDto("9/4/2022", "Pen2", 87, 2.65, 2.0));
    build
        .getAnimalAnalysis()
        .add(new BCSAnimalAnalysisRecordDto("10/4/2022", "Pen2", 120, 2.75, 2.0));
    build
        .getAnimalAnalysis()
        .add(new BCSAnimalAnalysisRecordDto("11/4/2022", "Pen2", 148, 2.9, 3.0));
    return build;
  }

  public static LocomotionAnimalAnalysisReportDto prepareDataForLocomotionAnimalAnalysis() {
    LocomotionAnimalAnalysisReportDto build =
        LocomotionAnimalAnalysisReportDto.builder()
            .fileName("animal analysis")
            .visitName("test visit")
            .visitDate("12/12")
            .toolName("locomotion")
            .analysisType("animal analysis")
            .animalTagName("bbbccccccwww")
            .calvingDate("12/12")
            .animalAnalysis(new ArrayList<>())
            .build();
    build
        .getAnimalAnalysis()
        .add(new LocomotionAnimalAnalysisRecordDto("7/4/2022", "Pen1", 14, 3.1, 1.0));
    build
        .getAnimalAnalysis()
        .add(new LocomotionAnimalAnalysisRecordDto("8/4/2022", "Pen1", 47, 2.7, 1.0));
    build
        .getAnimalAnalysis()
        .add(new LocomotionAnimalAnalysisRecordDto("9/4/2022", "Pen2", 87, 2.65, 2.0));
    build
        .getAnimalAnalysis()
        .add(new LocomotionAnimalAnalysisRecordDto("10/4/2022", "Pen2", 120, 2.75, 2.0));
    build
        .getAnimalAnalysis()
        .add(new LocomotionAnimalAnalysisRecordDto("11/4/2022", "Pen2", 148, 2.9, 3.0));
    return build;
  }

  public static RumenHealthMSHerdAnalysisReportDto prepareDataForRumenHealthMSHerdAnalysis() {
    RumenHealthMSHerdAnalysisReportDto build =
        RumenHealthMSHerdAnalysisReportDto.builder()
            .lactationStages(
                Arrays.stream(LactationStage.values()).map(Enum::name).toArray(String[]::new))
            .averageManureScore(new LinkedHashMap<>())
            .min(new LinkedHashMap<>())
            .max(new LinkedHashMap<>())
            // header details
            .visitName("lorem")
            .visitDate("21-12-22")
            .toolName("BCS")
            .analysisType("Heard Analysis")
            .fileName("BCSHeardAnalysis.xlsx")
            .build();

    //  average
    build.getAverageManureScore().put(LactationStage.FarOffDry.name(), 3.55);
    build.getAverageManureScore().put(LactationStage.CloseUpDry.name(), 3.50);
    build.getAverageManureScore().put(LactationStage.Fresh.name(), 3.14);
    build.getAverageManureScore().put(LactationStage.EarlyLactation.name(), 2.94);
    build.getAverageManureScore().put(LactationStage.PeakMilk.name(), 3.01);
    build.getAverageManureScore().put(LactationStage.MidLactation.name(), 3.24);
    build.getAverageManureScore().put(LactationStage.LateLactation.name(), 3.39);

    //  min
    build.getMin().put(LactationStage.FarOffDry.name(), 3.25);
    build.getMin().put(LactationStage.CloseUpDry.name(), 3.25);
    build.getMin().put(LactationStage.Fresh.name(), 2.75);
    build.getMin().put(LactationStage.EarlyLactation.name(), 2.5);
    build.getMin().put(LactationStage.PeakMilk.name(), 2.5);
    build.getMin().put(LactationStage.MidLactation.name(), 2.75);
    build.getMin().put(LactationStage.LateLactation.name(), 3.0);

    //  max
    build.getMax().put(LactationStage.FarOffDry.name(), 3.75);
    build.getMax().put(LactationStage.CloseUpDry.name(), 3.75);
    build.getMax().put(LactationStage.Fresh.name(), 3.25);
    build.getMax().put(LactationStage.EarlyLactation.name(), 3.0);
    build.getMax().put(LactationStage.PeakMilk.name(), 3.0);
    build.getMax().put(LactationStage.MidLactation.name(), 3.25);
    build.getMax().put(LactationStage.LateLactation.name(), 3.75);
    return build;
  }

  public static ReturnOverFeedReportDto prepareDataForReturnOverFeed() {
    return ReturnOverFeedReportDto.builder()
        .returnOverFeedLabel("Return Over Feed ($/cow/day)")
        .secondLabel("Total Feed Cost ($)")
        .thirdLabel("Total Revenue ($)")
        .toolName("Return Over Feed Tool")
        .visitDate("2025-03-15")
        .fileName("return_over_feed_march_report.xlsx")
        .visitName("March Site Visit")
        .xAxisLabel("Visit Date")
        .dataPoints(
            List.of(
                ReturnOverFeedDataPoints.builder()
                    .returnOverFeed(3.75)
                    .totalFeedCost(7.20)
                    .visitDate("Week 1")
                    .totalRevenue(10.95)
                    .build(),
                ReturnOverFeedDataPoints.builder()
                    .returnOverFeed(3.50)
                    .totalFeedCost(6.80)
                    .visitDate("Week 2")
                    .totalRevenue(10.30)
                    .build(),
                ReturnOverFeedDataPoints.builder()
                    .returnOverFeed(3.90)
                    .totalFeedCost(7.10)
                    .visitDate("Week 3")
                    .totalRevenue(11.00)
                    .build()))
        .build();
  }

  public static MetabolicIncidenceReportDto prepareDataForMetabolicIncidence()
      throws JsonProcessingException {
    MetabolicIncidenceReportDto build =
        MetabolicIncidenceReportDto.builder()
            .metabolicTypeKeys(
                Arrays.stream(MetabolicTypeKeys.values()).map(Enum::name).toArray(String[]::new))
            .goalPercentage(new LinkedHashMap<>())
            .incidencePercentage(new LinkedHashMap<>())
            .metabolicDisorderCostPerCow(new LinkedHashMap<>())
            // header details
            .visitName("lorem")
            .visitDate("21-12-22")
            .toolName("Metabolic Incidence")
            .fileName("metabolicIncidence.xlsx")
            .metabolicDisorderCostPerCowLabel("Metabolic Disorder Cost/Cow ($)")
            .graphType(MetabolicIncidenceGraphExportType.BOTH)
            .build();
    // goal
    build.getGoalPercentage().put(MetabolicTypeKeys.RetainedPlacenta.name(), 70.0);
    build.getGoalPercentage().put(MetabolicTypeKeys.Metritis.name(), 90.0);
    build.getGoalPercentage().put(MetabolicTypeKeys.DisplacedAbomasum.name(), 95.0);
    build.getGoalPercentage().put(MetabolicTypeKeys.Ketosis.name(), 78.0);
    build.getGoalPercentage().put(MetabolicTypeKeys.MilkFever.name(), 55.0);

    //  average
    build.getIncidencePercentage().put(MetabolicTypeKeys.RetainedPlacenta.name(), 3.55);
    build.getIncidencePercentage().put(MetabolicTypeKeys.Metritis.name(), 3.50);
    build.getIncidencePercentage().put(MetabolicTypeKeys.DisplacedAbomasum.name(), 3.14);
    build.getIncidencePercentage().put(MetabolicTypeKeys.Ketosis.name(), 2.94);
    build.getIncidencePercentage().put(MetabolicTypeKeys.MilkFever.name(), 3.01);
    build.getIncidencePercentage().put(MetabolicTypeKeys.Dystocia.name(), 3.24);
    build.getIncidencePercentage().put(MetabolicTypeKeys.DeathLoss.name(), 3.39);

    //  min
    List<MetabolicDisorderCostPerCowDto> costs = new ArrayList<>();
    costs.add(
        MetabolicDisorderCostPerCowDto.builder()
            .cost(5.0)
            .visitDate("2023-02-13T17:35:12.288213Z")
            .build());
    build.getMetabolicDisorderCostPerCow().put(MetabolicTypeKeys.RetainedPlacenta.name(), costs);

    return build;
  }

  public static RumenFillHealthPenAnalysisReportDto prepareDataForRumenFillHealthPenAnalysis() {
    RumenFillHealthPenAnalysisReportDto build =
        RumenFillHealthPenAnalysisReportDto.builder()
            .visitName("lorem")
            .visitDate("21-12-22")
            .toolName("Rumen Fill")
            .analysisType("Pen Analysis")
            .fileName("RumenFillPenAnalysisAnalysis.xlsx")
            .standardDeviation(12.02)
            .average(65.2)
            .penName("pen 1")
            .build();
    build.setCategories(new ArrayList<>());
    build.getCategories().add(new RumenFillHealthPenAnalysisCategoryDto("9/18", 2.0));
    build.getCategories().add(new RumenFillHealthPenAnalysisCategoryDto("9/19", 3.65));
    build.getCategories().add(new RumenFillHealthPenAnalysisCategoryDto("9/20", 2.0));
    build.getCategories().add(new RumenFillHealthPenAnalysisCategoryDto("9/21", 2.22));
    build.getCategories().add(new RumenFillHealthPenAnalysisCategoryDto("9/22", 2.21));
    build.getCategories().add(new RumenFillHealthPenAnalysisCategoryDto("9/23", 2.12));
    build.getCategories().add(new RumenFillHealthPenAnalysisCategoryDto("9/23", 2.3));

    return build;
  }

  public static RumenFillHealthHerdAnalysisReportDto prepareDataForRumenFillHealthHerdAnalysis() {
    RumenFillHealthHerdAnalysisReportDto build =
        RumenFillHealthHerdAnalysisReportDto.builder()
            .lactationStages(
                Arrays.stream(LactationStage.values()).map(Enum::name).toArray(String[]::new))
            .averageRumenFillScore(new LinkedHashMap<>())
            .min(new LinkedHashMap<>())
            .max(new LinkedHashMap<>())
            // header details
            .visitName("lorem")
            .visitDate("21-12-22")
            .toolName("RumenFill")
            .analysisType("Heard Analysis")
            .fileName("RumenFillHeardAnalysis.xlsx")
            .build();

    //  average
    build.getAverageRumenFillScore().put(LactationStage.FarOffDry.name(), 3.55);
    build.getAverageRumenFillScore().put(LactationStage.CloseUpDry.name(), 3.50);
    build.getAverageRumenFillScore().put(LactationStage.Fresh.name(), 3.14);
    build.getAverageRumenFillScore().put(LactationStage.EarlyLactation.name(), 2.94);
    build.getAverageRumenFillScore().put(LactationStage.PeakMilk.name(), 3.01);
    build.getAverageRumenFillScore().put(LactationStage.MidLactation.name(), 3.24);
    build.getAverageRumenFillScore().put(LactationStage.LateLactation.name(), 3.39);

    //  min
    build.getMin().put(LactationStage.FarOffDry.name(), 3.25);
    build.getMin().put(LactationStage.CloseUpDry.name(), 3.25);
    build.getMin().put(LactationStage.Fresh.name(), 2.75);
    build.getMin().put(LactationStage.EarlyLactation.name(), 2.5);
    build.getMin().put(LactationStage.PeakMilk.name(), 2.5);
    build.getMin().put(LactationStage.MidLactation.name(), 2.75);
    build.getMin().put(LactationStage.LateLactation.name(), 3.0);

    //  max
    build.getMax().put(LactationStage.FarOffDry.name(), 3.75);
    build.getMax().put(LactationStage.CloseUpDry.name(), 3.75);
    build.getMax().put(LactationStage.Fresh.name(), 3.25);
    build.getMax().put(LactationStage.EarlyLactation.name(), 3.0);
    build.getMax().put(LactationStage.PeakMilk.name(), 3.0);
    build.getMax().put(LactationStage.MidLactation.name(), 3.25);
    build.getMax().put(LactationStage.LateLactation.name(), 3.75);

    return build;
  }

  public static ForagePennStateReportDto prepareDataForForagePennStateHerdAnalysis() {
    ForagePennStateReportDto build =
        ForagePennStateReportDto.builder()
            .visitName("lorem")
            .visitDate("21-12-22")
            .toolName("Forage Penn State")
            .analysisType("Herd Analysis")
            .fileName("ForagePennStateHerdAnalysis.xlsx")
            .pspsLabel("PSPS 1 - corn")
            .scorerLabel("test score")
            .build();
    build.setOnScreenPercentage(new ArrayList<>());
    build
        .getOnScreenPercentage()
        .add(new ForagePennStateOnScreenPercentageDto("9/18", 43.0, 90.0, 85.0, 73.0));
    build
        .getOnScreenPercentage()
        .add(new ForagePennStateOnScreenPercentageDto("9/19", 43.0, 90.0, 85.0, 73.0));
    build
        .getOnScreenPercentage()
        .add(new ForagePennStateOnScreenPercentageDto("9/20", 43.0, 90.0, 85.0, 73.0));
    build
        .getOnScreenPercentage()
        .add(new ForagePennStateOnScreenPercentageDto("9/21", 43.0, 90.0, 85.0, 73.0));
    build
        .getOnScreenPercentage()
        .add(new ForagePennStateOnScreenPercentageDto("9/22", 43.0, 90.0, 85.0, 73.0));
    build
        .getOnScreenPercentage()
        .add(new ForagePennStateOnScreenPercentageDto("9/23", 43.0, 90.0, 85.0, 73.0));
    build
        .getOnScreenPercentage()
        .add(new ForagePennStateOnScreenPercentageDto("9/23", 43.0, 90.0, 85.0, 73.0));
    return build;
  }

  public static RumenHealthTMRParticleScorePenAnalysisReportDto
      prepareDataForRumenHealthTmrParticleScorePenAnalysis() {
    RumenHealthTMRParticleScorePenAnalysisReportDto build =
        RumenHealthTMRParticleScorePenAnalysisReportDto.builder()
            .visitName("lorem")
            .visitDate("21-12-22")
            .toolName("Rumen health tmr particle score")
            .analysisType("Pen Analysis")
            .fileName("rumenHealthTmrParticleScorePenAnalysis.xlsx")
            .tmrLabel("PSPS 1 - corn")
            .scorerLabel("score label")
            .build();
    build.setOnScreenPercentage(new ArrayList<>());
    build
        .getOnScreenPercentage()
        .add(new RumenHealthTMRParticleScoreOnScreenPercentageDto("9/18", 43.0, 90.0, 85.0, 73.0));
    build
        .getOnScreenPercentage()
        .add(new RumenHealthTMRParticleScoreOnScreenPercentageDto("9/19", 43.0, 90.0, 85.0, 73.0));
    build
        .getOnScreenPercentage()
        .add(new RumenHealthTMRParticleScoreOnScreenPercentageDto("9/20", 43.0, 90.0, 85.0, 73.0));
    build
        .getOnScreenPercentage()
        .add(new RumenHealthTMRParticleScoreOnScreenPercentageDto("9/21", 43.0, 90.0, 85.0, 73.0));
    build
        .getOnScreenPercentage()
        .add(new RumenHealthTMRParticleScoreOnScreenPercentageDto("9/22", 43.0, 90.0, 85.0, 73.0));
    build
        .getOnScreenPercentage()
        .add(new RumenHealthTMRParticleScoreOnScreenPercentageDto("9/23", 43.0, 90.0, 85.0, 73.0));
    build
        .getOnScreenPercentage()
        .add(new RumenHealthTMRParticleScoreOnScreenPercentageDto("9/23", 43.0, 90.0, 85.0, 73.0));
    return build;
  }

  public static RumenHealthTMRParticleScoreHerdAnalysisReportDto
      prepareDataForRumenHealthTmrParticleScoreHerdAnalysis() {
    RumenHealthTMRParticleScoreHerdAnalysisReportDto build =
        RumenHealthTMRParticleScoreHerdAnalysisReportDto.builder()
            .visitName("lorem")
            .visitDate("21-12-22")
            .toolName("Rumen health tmr particle score herd analysis")
            .analysisType("Herd Analysis")
            .fileName("rumenHealthTmrParticleScoreHerdAnalysis.xlsx")
            .build();
    build.setDataPoints(new ArrayList<>());
    build
        .getDataPoints()
        .add(new RumenHealthTMRParticleScoreHerdAnalysisDto("pen 1", 43.0, 90.0, 85.0, 73.0));
    build
        .getDataPoints()
        .add(new RumenHealthTMRParticleScoreHerdAnalysisDto("pen 2", 43.0, 90.0, 85.0, 73.0));
    build
        .getDataPoints()
        .add(new RumenHealthTMRParticleScoreHerdAnalysisDto("pen 3", 43.0, 90.0, 85.0, 73.0));
    build
        .getDataPoints()
        .add(new RumenHealthTMRParticleScoreHerdAnalysisDto("pen 4", 43.0, 90.0, 85.0, 73.0));
    build
        .getDataPoints()
        .add(new RumenHealthTMRParticleScoreHerdAnalysisDto("pen 5", 43.0, 90.0, 85.0, 73.0));
    build
        .getDataPoints()
        .add(new RumenHealthTMRParticleScoreHerdAnalysisDto("pen 6", 43.0, 90.0, 85.0, 73.0));
    build
        .getDataPoints()
        .add(new RumenHealthTMRParticleScoreHerdAnalysisDto("pen 7", 43.0, 90.0, 85.0, 73.0));
    return build;
  }

  public static VisitReportDto prepareDataForVisitReport() {

    return VisitReportDto.builder()
        .fileName("Visit Report")
        .bodyConditionScoreTool(
            VisitReportBodyConditionScoreDto.builder()
                .animalAnalysis(
                    Collections.singletonList(VisitReportAnimalDetailsDto.builder().build()))
                .herdAnalysis(
                    VisitReportBCSHerdAnalysisDto.builder()
                        .graph(BCSToolHeardAnalysisReportDto.builder().build())
                        .herdAnalysisDetails(
                            List.of(
                                Collections.singletonList(
                                    VisitReportColumnValueDto.builder()
                                        .column("")
                                        .value("")
                                        .build())))
                        .build())
                .notes(
                    List.of(
                        NotesDto.builder().id(UUID.randomUUID()).build(),
                        NotesDto.builder()
                            .title("test title")
                            .note(UUID.randomUUID().toString())
                            .build()))
                .build())
        .rumenHealthCudChewingTool(
            VisitReportRumenHealthCudChewingDto.builder()
                .penAnalysis(
                    Collections.singletonList(VisitReportRHCCPenAnalysisDto.builder().build()))
                .herdAnalysis(
                    VisitReportRHCCHerdAnalysisDto.builder()
                        .graphs(
                            CudChewingHerdAnalysisReportDto.builder()
                                .cudChewingPercentage(new LinkedHashMap<>())
                                .build())
                        .build())
                .notes(
                    Arrays.asList(
                        NotesDto.builder().id(UUID.randomUUID()).build(),
                        NotesDto.builder()
                            .title("test title")
                            .note(UUID.randomUUID().toString())
                            .build()))
                .build())
        .locomotionScoreTool(
            VisitReportLocomotionScoreDto.builder()
                .animalAnalysis(
                    Collections.singletonList(VisitReportAnimalDetailsDto.builder().build()))
                .herdAnalysis(
                    VisitReportLocomotionHerdAnalysisDto.builder()
                        .graph(LocomotionScoreHerdAnalysisReportDto.builder().build())
                        .herdAnalysisDetails(
                            List.of(
                                Collections.singletonList(
                                    VisitReportColumnValueDto.builder()
                                        .column("")
                                        .value("")
                                        .build())))
                        .build())
                .notes(
                    Arrays.asList(
                        NotesDto.builder().id(UUID.randomUUID()).build(),
                        NotesDto.builder()
                            .title("test title")
                            .note(UUID.randomUUID().toString())
                            .build()))
                .build())
        .rumenHealthManureScoreTool(
            VisitReportRumenHealthManureScoreDto.builder()
                .herdAnalysis(
                    VisitReportRumenHealthMSHerdAnalysisDto.builder()
                        .graph(RumenHealthMSHerdAnalysisReportDto.builder().build())
                        .build())
                .notes(
                    Arrays.asList(
                        NotesDto.builder().id(UUID.randomUUID()).build(),
                        NotesDto.builder()
                            .title("test title")
                            .note(UUID.randomUUID().toString())
                            .build()))
                .build())
        .milkSoldEvaluationTool(
            VisitReportMilkSoldEvaluationDto.builder()
                .graphs(Collections.singletonList(MilkSoldEvaluationReportDto.builder().build()))
                .notes(
                    Arrays.asList(
                        NotesDto.builder().id(UUID.randomUUID()).build(),
                        NotesDto.builder()
                            .title("test title")
                            .note(UUID.randomUUID().toString())
                            .build()))
                .build())
        .roboticMilkEvaluationTool(
            VisitReportRoboticMilkEvaluationDto.builder()
                .graphs(
                    Collections.singletonList(
                        RoboticMilkEvaluationReportDto.builder()
                            .dualYaxisGraph(
                                DualYAxisGraphDto.builder()
                                    .yleftDataPoints(
                                        Collections.singletonList(
                                            XAndYAxisValueDto.builder().x("43").y(34.0).build()))
                                    .yrightDataPoints(
                                        Collections.singletonList(
                                            XAndYAxisValueDto.builder().x("43").y(34.0).build()))
                                    .yleftLineColor(PresetColor.GREEN)
                                    .yrightLineColor(PresetColor.GREEN)
                                    .build())
                            .singleYaxisGraph(
                                SingleYAxisGraphDto.builder()
                                    .yleftDataPoints(
                                        Collections.singletonList(
                                            XAndYAxisValueDto.builder().x("43").y(34.0).build()))
                                    .yleftLineColor(PresetColor.GREEN)
                                    .build())
                            .build()))
                .analysisDialGraphs(
                    Collections.singletonList(
                        DialGraphReportDto.builder()
                            .gaugeTotal(3.18)
                            .gaugeColors(
                                Collections.singletonList(
                                    GaugeColorDto.builder()
                                        .hexColor("#ffffff")
                                        .endAngle(1.12)
                                        .startAngle(2.22)
                                        .build()))
                            .build()))
                .herdLevelInformationLeftTable(new LinkedHashMap<>())
                .herdLevelInformationRightTable(new LinkedHashMap<>())
                .outputTable(new LinkedHashMap<>())
                .notes(
                    Arrays.asList(
                        NotesDto.builder().id(UUID.randomUUID()).build(),
                        NotesDto.builder()
                            .title("test title")
                            .note(UUID.randomUUID().toString())
                            .build()))
                .build())
        .metabolicIncidenceTool(
            VisitReportMetabolicIncidenceDto.builder()
                .notes(
                    Arrays.asList(
                        NotesDto.builder().id(UUID.randomUUID()).build(),
                        NotesDto.builder()
                            .title("test title")
                            .note(UUID.randomUUID().toString())
                            .build()))
                .graph(
                    MetabolicIncidenceReportDto.builder()
                        .graphType(MetabolicIncidenceGraphExportType.BOTH)
                        .metabolicDisorderCostPerCow(new LinkedHashMap<>())
                        .goalPercentage(new LinkedHashMap<>())
                        .build())
                .build())
        .rumenFillTool(
            VisitReportRumenFillDto.builder()
                .notes(
                    Arrays.asList(
                        NotesDto.builder().id(UUID.randomUUID()).build(),
                        NotesDto.builder()
                            .title("test title")
                            .note(UUID.randomUUID().toString())
                            .build()))
                .herdAnalysis(
                    VisitReportRumenFillHerdAnalysisDto.builder()
                        .graph(RumenFillHealthHerdAnalysisReportDto.builder().build())
                        .herdAnalysisDetails(
                            List.of(
                                Collections.singletonList(
                                    VisitReportColumnValueDto.builder().build())))
                        .build())
                .penAnalysis(
                    Collections.singletonList(VisitReportRumenFillPenAnalysisDto.builder().build()))
                .build())
        .forageInventoriesTool(
            VisitReportForageInventoriesDto.builder()
                .details(List.of(new LinkedHashMap<>()))
                .notes(
                    Arrays.asList(
                        NotesDto.builder().id(UUID.randomUUID()).build(),
                        NotesDto.builder()
                            .title("test title")
                            .note(UUID.randomUUID().toString())
                            .build()))
                .build())
        .foragePennStateTool(
            VisitReportForagePennStateDto.builder()
                .details(List.of(new LinkedHashMap<>()))
                .notes(
                    Arrays.asList(
                        NotesDto.builder().id(UUID.randomUUID()).build(),
                        NotesDto.builder()
                            .title("test title")
                            .note(UUID.randomUUID().toString())
                            .build()))
                .build())
        .rumenHealthTMRParticleScoreTool(
            VisitReportRumenHealthTMRParticleScoreDto.builder()
                .penAnalysis(Collections.emptyList())
                .herdAnalysis(
                    VisitReportRumenHealthTMRParticleScoreHerdAnalysisDto.builder().build())
                .notes(
                    Arrays.asList(
                        NotesDto.builder().id(UUID.randomUUID()).build(),
                        NotesDto.builder()
                            .title("test title")
                            .note(UUID.randomUUID().toString())
                            .build()))
                .build())
        .rumenHealthManureScreeningTool(
            VisitReportRumenHealthManureScreeningDto.builder()
                .penAnalysis(
                    VisitReportRumenHealthManureScreeningPenAnalysisDto.builder()
                        .graph(Collections.emptyList())
                        .build())
                .notes(
                    Arrays.asList(
                        NotesDto.builder().id(UUID.randomUUID()).build(),
                        NotesDto.builder()
                            .title("test title")
                            .note(UUID.randomUUID().toString())
                            .build()))
                .build())
        .heatStressEvaluationTool(
            VisitReportHeatStressEvaluationDto.builder()
                .notes(
                    Arrays.asList(
                        NotesDto.builder().id(UUID.randomUUID()).build(),
                        NotesDto.builder()
                            .title("test title")
                            .note(UUID.randomUUID().toString())
                            .build()))
                .build())
        .forageAuditTool(
            VisitReportForageAuditDto.builder()
                .notes(
                    Arrays.asList(
                        NotesDto.builder().id(UUID.randomUUID()).build(),
                        NotesDto.builder()
                            .title("test title")
                            .note(UUID.randomUUID().toString())
                            .build()))
                .build())
        .penTimeBudgetTool(
            VisitReportPenTimeBudgetDto.builder()
                .penAnalysis(
                    Collections.singletonList(
                        VisitReportPenTimeBudgetPenAnalysisDto.builder()
                            .timeAvailableForRestingGraph(
                                PenTimeBudgetTimeAvailableForRestingReportDto.builder()
                                    .timeRequired(new ArrayList<>())
                                    .timeRemaining(new ArrayList<>())
                                    .build())
                            .potentialMilkLossGainGraph(
                                PenTimeBudgetPotentialMilkLossGainReportDto.builder().build())
                            .build()))
                .notes(
                    Arrays.asList(
                        NotesDto.builder().id(UUID.randomUUID()).build(),
                        NotesDto.builder()
                            .title("test title")
                            .note(UUID.randomUUID().toString())
                            .build()))
                .build())
        .scorecardTool(
            VisitReportScorecardDto.builder()
                .notes(
                    Arrays.asList(
                        NotesDto.builder().id(UUID.randomUUID()).build(),
                        NotesDto.builder()
                            .title("test title")
                            .note(UUID.randomUUID().toString())
                            .build()))
                .build())
        .build();
  }

  public static PenTimeBudgetTimeAvailableForRestingReportDto
      prepareDataForPenTimeBudgetTimeAvailableForResting() {
    return PenTimeBudgetTimeAvailableForRestingReportDto.builder()
        .categoryLabel("Time Available for Resting")
        .fileName("TimeAvailableForResting")
        .timeRemaining(new ArrayList<>())
        .timeRequired(new ArrayList<>())
        .toolName("PenTime Budget")
        .visitDate("20th June")
        .visitName("Test Visit")
        .build();
  }

  public static PenTimeBudgetPotentialMilkLossGainReportDto
      prepareDataForPenTimeBudgetMilkLossGain() {

    return PenTimeBudgetPotentialMilkLossGainReportDto.builder()
        .categoryLabel("Time Available for Resting")
        .fileName("TimeAvailableForResting")
        .dataPoints(List.of(XAndYAxisValueDto.builder().x("19/11").y(20.0).build()))
        .sheetName("test")
        .toolName("Pentime Budget")
        .visitDate("20th june")
        .visitName("test visit")
        .build();
  }

  public static RumenHealthManureScreeningPenAnalysisReportDto
      prepareDataForRumenHealthManureScreeningPenAnalysis() {
    return RumenHealthManureScreeningPenAnalysisReportDto.builder()
        .visitName("13th June Pen Time Budget")
        .visitDate("13th June,2023")
        .toolName("Rumen Health Manure Screening")
        .analysisType("Pen Analysis")
        .fileName("RumenHealthManureScreening")
        .onScreenPercentage(
            List.of(RumenHealthManureScreeningOnScreenPercentageDto.builder().build()))
        .build();
  }

  public static HeatStressReportDto prepareDataForHeatStressReport() {
    // TODO Auto-generated method stub
    return HeatStressReportDto.builder()
        .fileName("TimeAvailableForResting")
        .toolName("Pentime Budget")
        .visitDate("20th june")
        .unitOfMeasure("Metric")
        .visitName("test visit")
        .data(
            HeatStressDataPointsDto.builder()
                .dmiAdjustmentPercentage(20.3)
                .energyEquivalentMilkLoss(30.3)
                .estimatedDryMatterIntake(40.5)
                .inTakeAdjustment(32.3)
                .inTakeAdjustment(40.3)
                .lossOfEnergyConsumed(12.5)
                .milkValueLossPerDay(20.5)
                .milkValueLossPerMonth(25000.1)
                .reductionInDmi(30.3)
                .stressColour(IndexedColors.GREEN)
                .temperatureInCelsius(21.3)
                .temperatureInFahrenheit(42.6)
                .build())
        .build();
  }
}
