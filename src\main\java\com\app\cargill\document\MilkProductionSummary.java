/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@SuppressWarnings("java:S125")
public class MilkProductionSummary implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("AverageMilkProductionAnimalsInTankKg")
  private Double averageMilkProductionAnimalsInTankKg;

  @JsonProperty("AverageMilkProductionLitresPerCowPerDay")
  private Double averageMilkProductionLitresPerCowPerDay;

  @JsonProperty("DairyEfficiency")
  private Double dairyEfficiency;

  @JsonProperty("Butterfat")
  private MilkComponent butterfat;

  @JsonProperty("Protein")
  private MilkComponent protein;

  @JsonProperty("LactoseAndOtherSolids")
  private MilkComponent lactoseAndOtherSolids;

  // Getters and setters...
}
