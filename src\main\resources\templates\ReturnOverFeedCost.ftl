<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office"><head>
	<meta http-equiv="Content-type" content="text/html; charset=utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="format-detection" content="date=no">
	<meta name="format-detection" content="address=no">
	<meta name="format-detection" content="telephone=no">
	<meta name="x-apple-disable-message-reformatting">
	<link href="../../app/generatedTemplateResources/css/main.css" rel="stylesheet">
	<script src="../../app/generatedTemplateResources/js/chart.js"></script>
	<title>Cargill</title>
</head>
<body>

	<div class="container">
		<div class="template-header">
			<figure>
				<img src="../../app/generatedTemplateResources/images/cargill-logo.svg">
			</figure>
		</div>

		<div class="card mb-5">
			<div class="card-header pt-5">
				<h4 class="mb-2">${model.visitName!}</h4>

				<div class="row">
					<div class="content-set">
						<label>${localization.getMessage("Report.Tool.Name", [], "Tool Name", locale)}:</label>
						<h4>${model.toolName!}</h4>
					</div>
					<div class="content-set mx-2">
						<label>${localization.getMessage("Report.Visit.Date", [], "Visit Date", locale)}:</label>
						<h4>${model.visitDate!}</h4>
					</div>
					<div id="region" style="opacity: 0;">${region}</div>
				</div>
			</div>

			<div class="card-body">
				<div class="row">
					<div class="legend-wrap mb-2">
						<p class="tropic-wave-cyan">${model.returnOverFeedLabel}</p>
					</div>
					<div class="legend-wrap mb-2">
						<p class="sky-breeze-blue">${model.secondLabel}</p>
					</div>
					<div class="legend-wrap mb-2">
						<p class="lavender-mist">${model.thirdLabel}</p>
					</div>
				</div>

				<canvas id="linechart"></canvas>
			</div>

		</div>
	</div>


<script>

const bar1 = [
	<#list model.dataPoints as val>
	     ${(val.returnOverFeed)!'NaN'}<#sep>, </#sep>
	</#list>
];

const bar2 = [
	<#list model.dataPoints as val>
	     ${(val.totalFeedCost)!'NaN'}<#sep>, </#sep>
	</#list>
];

const bar3 = [
	<#list model.dataPoints as val>
	     ${(val.totalRevenue)!'NaN'}<#sep>, </#sep>
	</#list>
];

<!-- DEBUG: Complete Model Analysis -->
<div style="display: none; white-space: pre-wrap;">
=== MODEL DEBUG INFO ===
Model Type: ${model.class.name!'UNKNOWN'}

=== TOP LEVEL PROPERTIES ===
visitName: ${model.visitName!'NULL'}
visitDate: ${model.visitDate!'NULL'}
fileName: ${model.fileName!'NULL'}
toolName: ${model.toolName!'NULL'}
returnOverFeedLabel: ${model.returnOverFeedLabel!'NULL'}
secondLabel: ${model.secondLabel!'NULL'}
thirdLabel: ${model.thirdLabel!'NULL'}
xAxisLabel: ${model.xAxisLabel!'NULL'}

=== DATAPOINTS ANALYSIS ===
dataPoints exists: ${model.dataPoints???string('YES', 'NO')}
<#if model.dataPoints??>
dataPoints type: ${model.dataPoints.class.name!'UNKNOWN'}
dataPoints size: ${model.dataPoints?size!'0'}
</#if>

<#if model.dataPoints?? && (model.dataPoints?size > 0)>
=== INDIVIDUAL DATAPOINTS ===
<#list model.dataPoints as dataPoint>
DataPoint ${dataPoint_index}:
  Type: ${dataPoint.class.name!'UNKNOWN'}
  xAxis: '${dataPoint.xAxis!'NULL'}'
  returnOverFeed: ${dataPoint.returnOverFeed!'NULL'}
  totalFeedCost: ${dataPoint.totalFeedCost!'NULL'}
  totalRevenue: ${dataPoint.totalRevenue!'NULL'}

</#list>
<#else>
NO DATAPOINTS FOUND OR EMPTY
</#if>

=== RAW MODEL KEYS ===
<#list model?keys as key>
${key}: ${model[key]!'NULL'}
</#list>
</div>

const xAxis = [
<#list model.dataPoints as val>
    '${(val.xAxis)!}'<#sep>, </#sep>
</#list>
];

const ctx = document.getElementById("linechart").getContext("2d");
ctx.canvas.height = 200;

const options = {
type: "bar",
data: {
labels: xAxis,
datasets: [
		{
			data: bar1,
			grouped:true,
			backgroundColor: "#7AD8DC",
			barThickness: 20,
			borderColor: "rgba(0,0,0,0)",
			skipNull : true,
			borderWidth: 4,
		},
		
		{
			data: bar2,
			backgroundColor: "#83BEF4",
			barThickness: 20,
			borderColor: "rgba(0,0,0,0)",
			skipNull : true,
			borderWidth: 4,
		},

		{
			data: bar3,
			backgroundColor: "#ABA1E3",
			barThickness: 20,
			borderColor: "rgba(0,0,0,0)",
			skipNull : true,
			borderWidth: 4,
		}
    ]
  },
  options: {
	plugins: {
	legend: {
	display: false,
	},
		tooltip: {
		callbacks: {
		title : () => null // or function () { return null; }
         },
			yAlign: 'bottom',
			backgroundColor: "#fff",
			borderColor: "rgba(0, 0, 0, 0.25)",
			borderWidth: 1,
			displayColors: false,
			bodyColor: "#307698",
			bodyAlign: "center",
        },
  	},

    layout: {
		padding: {
			top:20,
			right: 15
		}
	},

    responsive: true,

    scales: {
		y: {
			// beginAtZero: true,
			title: {
			display: true,
			color: '#6C7782',
			text: '${localization.getMessage("Report.Return.Over.Feed.YAxis", [], " Return Over Feed ($/cow/day)", locale)}',
			padding: {
				bottom: 15,
			}
    	},
			
		grid: {
			display: false,
		},
		ticks: {
			// Include a % sign in the ticks
			callback: function(value, index, ticks) {
			return  value + '%';
			}
		}
    },

		x: {
			title: {
			display: true,
			color: '#6C7782',
			 text: '${localization.getMessage("Report.Visit.Date", [], "Visit Date", locale)}',
			padding: {
				top: 15,
			}
      		},
			grid: {
				display: false,
			},
		}
    },
	animation: {
	duration: 0,
	onComplete: function() {
		var chart = this;
		var ctx = chart.ctx;
		ctx.textAlign = 'center';
		ctx.textBaseline = 'bottom';
		ctx.fillStyle =  '#6C7782';
		this.data.datasets.forEach(function(dataset, i) {
						var meta = chart.getDatasetMeta(i);
						meta.data.forEach(function(bar, index) {
						var data = dataset.data[index];
						let regionText = document.getElementById("region").innerHTML;
						data = isNaN(data) ? '': new Intl.NumberFormat(regionText).format(data) + '%';
						var yIndex = bar.y - 5;
						if(data && data < 0) {
							yIndex = bar.y + 15;
						}
						var fontSize = Math.round(chart.chartArea.width / dataset.data.length / 3);
                        if (fontSize > 12) {
                            fontSize = 12;
                        } else if (fontSize < 5) {
                            fontSize = 5;
                        }
                        ctx.font = fontSize + 'px "Helvetica Neue", Helvetica, Arial, sans-serif'
                        ctx.save();
                        // Translate 0,0 to the point you want the text
                        ctx.translate(bar.x + 5, yIndex);
						// Rotate context by -90 degrees
                        ctx.rotate(-0.4 * Math.PI);
                        ctx.textAlign = "center";
						ctx.fillText(data, 5, 0);
						ctx.restore();
                        chart.resize();
						});
				});
			}
		}
  }
};

window.onload = function () {
window.myLine = new Chart(ctx, options);
};

</script>

</body>
</html>