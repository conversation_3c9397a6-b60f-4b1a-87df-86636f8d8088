/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.components.FreeMarkerComponent;
import com.app.cargill.constants.ExportFileExtensions;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ReportsToBeanMappings;
import com.app.cargill.constants.TemplateExportType;
import com.app.cargill.dto.ReturnOverFeedDataPoints;
import com.app.cargill.dto.ReturnOverFeedReportDto;
import com.app.cargill.service.IExcelReportService;
import com.app.cargill.utils.ExcelUtils;
import com.app.cargill.utils.ZipUtils;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xddf.usermodel.chart.BarDirection;
import org.apache.poi.xddf.usermodel.chart.ChartTypes;
import org.apache.poi.xddf.usermodel.chart.XDDFBarChartData;
import org.apache.poi.xddf.usermodel.chart.XDDFCategoryAxis;
import org.apache.poi.xddf.usermodel.chart.XDDFDataSource;
import org.apache.poi.xddf.usermodel.chart.XDDFDataSourcesFactory;
import org.apache.poi.xddf.usermodel.chart.XDDFNumericalDataSource;
import org.apache.poi.xddf.usermodel.chart.XDDFValueAxis;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFChart;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.modelmapper.ModelMapper;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service("returnOverFeedReportServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S125", "java:S3776"}) // remove when all commented code is removed
public class ReturnOverFeedReportServiceImpl implements IExcelReportService {

  private final ModelMapper modelMapper;
  ResourceBundleMessageSource source;
  private final FreeMarkerComponent freeMarkerComponent;

  @Override
  public String getFileName(Object data) {
    ReturnOverFeedReportDto dto = modelMapper.map(data, ReturnOverFeedReportDto.class);
    return StringUtils.isBlank(dto.getFileName())
        ? ReportsToBeanMappings.RETURN_OVER_FEED.getFileName()
        : dto.getFileName();
  }

  @Override
  public ByteArrayResource prepareExportToExcel(
      Object dto, ResourceBundleMessageSource source, Locale locale) throws IOException {
    this.source = source;
    ReturnOverFeedReportDto mappedDto = modelMapper.map(dto, ReturnOverFeedReportDto.class);

    try (XSSFWorkbook returnOverFeedWB = new XSSFWorkbook()) {
      XSSFCellStyle boldStyle =
          ExcelUtils.applyCellStyle(
              returnOverFeedWB,
              null,
              null,
              HorizontalAlignment.RIGHT,
              ExcelUtils.getFont(returnOverFeedWB, false, true, IndexedColors.BLACK));

      XSSFCellStyle greyCellStyle =
          ExcelUtils.applyCellStyle(
              returnOverFeedWB,
              IndexedColors.GREY_25_PERCENT,
              FillPatternType.SOLID_FOREGROUND,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(returnOverFeedWB, false, true, IndexedColors.BLACK));

      XSSFCellStyle centerBlack =
          ExcelUtils.applyCellStyle(
              returnOverFeedWB,
              null,
              null,
              HorizontalAlignment.CENTER,
              ExcelUtils.getFont(returnOverFeedWB, false, true, IndexedColors.BLACK));

      // Create the main sheet
      XSSFSheet sheet =
          addReturnOverFeedSheet(
              returnOverFeedWB, source, locale, mappedDto, boldStyle, greyCellStyle, centerBlack);

      int totalSheetColumns = sheet.getLastRowNum();
      return ExcelUtils.finalizeWorkbook(returnOverFeedWB, totalSheetColumns);

    } catch (IOException e) {
      log.error(e.getLocalizedMessage());
      throw new IOException(e.getLocalizedMessage());
    }
  }

  private XSSFSheet addReturnOverFeedSheet(
      XSSFWorkbook returnOverFeedWB,
      ResourceBundleMessageSource source,
      Locale locale,
      ReturnOverFeedReportDto returnOverFeedDto,
      XSSFCellStyle boldStyle,
      XSSFCellStyle greyCellStyle,
      XSSFCellStyle centerBlack) {

    XSSFCellStyle decimalStyle =
        ExcelUtils.decimalCellStyle(
            returnOverFeedWB, false, IndexedColors.BLACK, HorizontalAlignment.CENTER);

    XSSFSheet returnOverFeedWBSheet =
        returnOverFeedWB.createSheet(
            ExcelUtils.getLangValue(LangKeys.REPORT_SHEET_NAME, "Report", null, source, locale)
                .replace("/", "⧸"));

    AtomicInteger rowNumber = new AtomicInteger(0);
    AtomicInteger cellNumber = new AtomicInteger(0);

    // Add header information
    prepareHeader(
        returnOverFeedWB,
        returnOverFeedWBSheet,
        rowNumber,
        cellNumber,
        returnOverFeedDto,
        boldStyle,
        locale);

    // Create data table title
    cellNumber.set(0);
    XSSFRow row = returnOverFeedWBSheet.createRow(rowNumber.getAndIncrement());
    ExcelUtils.createAndSetCellValue(row, cellNumber, greyCellStyle, "Return Over Feed Data");
    returnOverFeedWBSheet.addMergedRegion(
        new CellRangeAddress(
            rowNumber.get() - 1, rowNumber.get() - 1, 0, returnOverFeedDto.getDataPoints().size()));

    // Create period headers (Week 1, Week 2, etc.)
    int periodHeaderRowNumber = rowNumber.get();
    cellNumber.set(1);
    row = returnOverFeedWBSheet.createRow(rowNumber.getAndIncrement());

    for (ReturnOverFeedDataPoints dataPoint : returnOverFeedDto.getDataPoints()) {
      ExcelUtils.highlightEmptyCell(
          row, dataPoint.getVisitDate(), cellNumber, centerBlack, greyCellStyle);
    }

    // Create data rows for the three metrics
    int dataStartRowNumber = rowNumber.get();
    int metricCount = 3; // returnOverFeed, totalFeedCost, totalRevenue

    String[] metricLabels = {
      returnOverFeedDto.getReturnOverFeedLabel(),
      returnOverFeedDto.getSecondLabel(),
      returnOverFeedDto.getThirdLabel()
    };

    for (int metricIndex = 0; metricIndex < metricCount; metricIndex++) {
      cellNumber.set(0);
      row = returnOverFeedWBSheet.createRow(rowNumber.getAndIncrement());

      // Set metric label
      ExcelUtils.createAndSetCellValue(row, cellNumber, centerBlack, metricLabels[metricIndex]);

      // Set values for each data point
      for (ReturnOverFeedDataPoints dataPoint : returnOverFeedDto.getDataPoints()) {
        Double value = null;
        if (metricIndex == 0) {
          value = dataPoint.getReturnOverFeed();
        } else if (metricIndex == 1) {
          value = dataPoint.getTotalFeedCost();
        } else if (metricIndex == 2) {
          value = dataPoint.getTotalRevenue();
        }
        ExcelUtils.highlightEmptyCell(row, value, cellNumber, decimalStyle, greyCellStyle);
      }
    }

    // Create chart
    createReturnOverFeedChart(
        returnOverFeedWBSheet,
        returnOverFeedDto,
        source,
        locale,
        periodHeaderRowNumber,
        dataStartRowNumber,
        metricCount);

    return returnOverFeedWBSheet;
  }

  private void createReturnOverFeedChart(
      XSSFSheet returnOverFeedWBSheet,
      ReturnOverFeedReportDto returnOverFeedDto,
      ResourceBundleMessageSource source,
      Locale locale,
      int periodHeaderRowNumber,
      int dataStartRowNumber,
      int metricCount) {

    // Calculate chart positioning
    int columnStart = 1;
    int columnEnd = columnStart + returnOverFeedDto.getDataPoints().size() - 1;
    columnEnd = ExcelUtils.fixColumnEndIndex(columnStart, columnEnd);

    int chartCol0 = columnEnd + 1;

    // Create chart
    XSSFChart chart =
        ExcelUtils.initChart(
            returnOverFeedWBSheet,
            "Return Over Feed Analysis",
            chartCol0,
            3,
            chartCol0
                + (returnOverFeedDto.getDataPoints().size() > 10
                    ? returnOverFeedDto.getDataPoints().size()
                    : 10),
            23);

    ExcelUtils.initLegends(chart);

    // Create axes
    XDDFCategoryAxis bottomAxis =
        ExcelUtils.createBottomAxis(
            chart,
            returnOverFeedDto.getXAxisLabel() != null ? returnOverFeedDto.getXAxisLabel() : "");
    XDDFValueAxis leftAxis =
        ExcelUtils.createLeftAxis(
            chart,
            ExcelUtils.getLangValue(LangKeys.REPORT_RETURN_OVER_FEED_YAXIS, null, source, locale));
    leftAxis.setCrosses(org.apache.poi.xddf.usermodel.chart.AxisCrosses.AUTO_ZERO);
    leftAxis.setCrossBetween(org.apache.poi.xddf.usermodel.chart.AxisCrossBetween.BETWEEN);

    // Create chart data
    XDDFBarChartData dataLeft =
        (XDDFBarChartData) chart.createData(ChartTypes.BAR, bottomAxis, leftAxis);
    dataLeft.setBarDirection(BarDirection.COL);

    // Create data source for categories (periods)
    XDDFDataSource<String> periodDataSource =
        XDDFDataSourcesFactory.fromStringCellRange(
            returnOverFeedWBSheet,
            new CellRangeAddress(
                periodHeaderRowNumber, periodHeaderRowNumber, columnStart, columnEnd));

    // Create series for each metric
    for (int metricIndex = 0; metricIndex < metricCount; metricIndex++) {
      XDDFNumericalDataSource<Double> metricDataSource =
          XDDFDataSourcesFactory.fromNumericCellRange(
              returnOverFeedWBSheet,
              new CellRangeAddress(
                  dataStartRowNumber + metricIndex,
                  dataStartRowNumber + metricIndex,
                  columnStart,
                  columnEnd));

      org.apache.poi.xddf.usermodel.chart.XDDFBarChartData.Series series =
          (org.apache.poi.xddf.usermodel.chart.XDDFBarChartData.Series)
              dataLeft.addSeries(periodDataSource, metricDataSource);

      series.setTitle(
          returnOverFeedWBSheet
              .getRow(dataStartRowNumber + metricIndex)
              .getCell(0)
              .getStringCellValue(),
          new org.apache.poi.ss.util.CellReference(
              returnOverFeedWBSheet.getSheetName(),
              dataStartRowNumber + metricIndex,
              0,
              true,
              true));
    }

    chart.plot(dataLeft);

    // Set colors for the bars
    java.util.List<byte[]> colors =
        java.util.Arrays.asList(
            new byte[] {(byte) 54, (byte) 162, (byte) 235}, // Blue
            new byte[] {(byte) 255, (byte) 99, (byte) 132}, // Red
            new byte[] {(byte) 75, (byte) 192, (byte) 192} // Green
            );
    ExcelUtils.setBarColorInBarChart(chart, colors, metricCount);
  }

  private void prepareHeader(
      XSSFWorkbook returnOverFeedWB,
      XSSFSheet returnOverFeedWBSheet,
      AtomicInteger rowNumber,
      AtomicInteger cellNumber,
      ReturnOverFeedReportDto returnOverFeedDto,
      XSSFCellStyle boldStyle,
      Locale locale) {

    // Add Cargill logo
    ExcelUtils.addCargillLogo(this.getClass(), returnOverFeedWB, returnOverFeedWBSheet, 0, 0);

    // Skip rows for logo
    rowNumber.set(3);

    // Visit Name
    XSSFRow row = returnOverFeedWBSheet.createRow(rowNumber.getAndIncrement());
    cellNumber.set(0);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_NAME, "Visit Name", null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, returnOverFeedDto.getVisitName());

    // Visit Date
    row = returnOverFeedWBSheet.createRow(rowNumber.getAndIncrement());
    cellNumber.set(0);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_VISIT_DATE, "Visit Date", null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, returnOverFeedDto.getVisitDate());

    // Tool Name
    row = returnOverFeedWBSheet.createRow(rowNumber.getAndIncrement());
    cellNumber.set(0);
    ExcelUtils.createAndSetCellValue(
        row,
        cellNumber,
        boldStyle,
        ExcelUtils.getLangValue(LangKeys.REPORT_TOOL_NAME, "Tool Name", null, source, locale));
    ExcelUtils.createAndSetCellValue(row, cellNumber, null, returnOverFeedDto.getToolName());

    // Add empty row
    rowNumber.getAndIncrement();
  }

  @Override
  public ByteArrayResource prepareExportToImage(
      Object dto, ResourceBundleMessageSource source, Locale locale)
      throws IOException, URISyntaxException {
    this.source = source;
    ReturnOverFeedReportDto mappedDto = modelMapper.map(dto, ReturnOverFeedReportDto.class);
    Map<String, byte[]> imageTemplates = new HashMap<>();

    // create sheet 1
    byte[] rumenHealthTmrParticleScore =
        freeMarkerComponent.render(
            mappedDto,
            ReportsToBeanMappings.RETURN_OVER_FEED.getImageTemplateName0(),
            source,
            locale,
            TemplateExportType.EXPORT_IMAGE);

    imageTemplates.put(
        ExcelUtils.getLangValue(
            LangKeys.REPORT_RUMEN_HEALTH_TMR_PARTICLE_SCORE,
            "Rumen Health TMR Particle Score",
            null,
            source,
            locale),
        rumenHealthTmrParticleScore);

    return new ByteArrayResource(
        ZipUtils.zipIfMultipleFiles(imageTemplates, ExportFileExtensions.PNG.getExtension()));
  }
}
